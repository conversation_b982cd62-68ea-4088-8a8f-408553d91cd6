// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ItemTicket struct {
	Id       int64 `json:"id"`
	SubName  int64 `json:"subName"`
	Duration int64 `json:"duration"`
}

var lockItemTicket sync.RWMutex
var storeItemTicket sync.Map
var strItemTicket string = "item_ticket"

func InitItemTicketCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strItemTicket, watchItemTicketFunc)
	return LoadAllItemTicketCfg()
}

func fixKeyItemTicket(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strItemTicket)
}
func watchItemTicketFunc(key string, js string) {
	mapItemTicket := make(map[int64]*ItemTicket)
	errUnmarshal := json.Unmarshal([]byte(js), &mapItemTicket)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeItemTicket.Store(key, mapItemTicket)
}

func GetAllItemTicket(option ...consulconfig.Option) map[int64]*ItemTicket {
	fitKey := fixKeyItemTicket(option...)
	store, ok := storeItemTicket.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemTicket)
		if ok {
			return storeMap
		}
	}
	lockItemTicket.Lock()
	defer lockItemTicket.Unlock()
	store, ok = storeItemTicket.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemTicket)
		if ok {
			return storeMap
		}
	}
	tblItemTicket := make(map[int64]*ItemTicket)
	item_ticket_str, err := consulconfig.GetInstance().GetConfig(strItemTicket, option...)
	if item_ticket_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_ticket_str), &tblItemTicket)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item_ticket", errUnmarshal)
		return nil
	}
	storeItemTicket.Store(fitKey, tblItemTicket)
	return tblItemTicket
}

func GetItemTicket(id int64, option ...consulconfig.Option) *ItemTicket {
	fitKey := fixKeyItemTicket(option...)
	store, ok := storeItemTicket.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemTicket)
		if ok {
			return storeMap[id]
		}
	}
	lockItemTicket.Lock()
	defer lockItemTicket.Unlock()
	store, ok = storeItemTicket.Load(fitKey)
	if ok {
		tblItemTicket, ok := store.(*ItemTicket)
		if ok {
			return tblItemTicket
		}
	}
	tblItemTicket := make(map[int64]*ItemTicket)
	item_ticket_str, err := consulconfig.GetInstance().GetConfig(strItemTicket, option...)
	if item_ticket_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_ticket_str), &tblItemTicket)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item_ticket", errUnmarshal)
		return nil
	}
	storeItemTicket.Store(fitKey, tblItemTicket)
	return tblItemTicket[id]
}

func LoadAllItemTicketCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strItemTicket, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ItemTicket", successChannels)
	return nil
}
