// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ClientRewardRewards struct {
	ItemId int64 `json:"itemId"`
	Count  int64 `json:"count"`
}

type ClientReward struct {
	Id      int64                 `json:"id"`
	Rewards []ClientRewardRewards `json:"rewards"`
}

var lockClientReward sync.RWMutex
var storeClientReward sync.Map
var strClientReward string = "client_reward"

func InitClientRewardCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strClientReward, watchClientRewardFunc)
	return LoadAllClientRewardCfg()
}

func fixKeyClientReward(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strClientReward)
}
func watchClientRewardFunc(key string, js string) {
	mapClientReward := make(map[int64]*ClientReward)
	errUnmarshal := json.Unmarshal([]byte(js), &mapClientReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeClientReward.Store(key, mapClientReward)
}

func GetAllClientReward(option ...consulconfig.Option) map[int64]*ClientReward {
	fitKey := fixKeyClientReward(option...)
	store, ok := storeClientReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ClientReward)
		if ok {
			return storeMap
		}
	}
	lockClientReward.Lock()
	defer lockClientReward.Unlock()
	store, ok = storeClientReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ClientReward)
		if ok {
			return storeMap
		}
	}
	tblClientReward := make(map[int64]*ClientReward)
	client_reward_str, err := consulconfig.GetInstance().GetConfig(strClientReward, option...)
	if client_reward_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(client_reward_str), &tblClientReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "client_reward", errUnmarshal)
		return nil
	}
	storeClientReward.Store(fitKey, tblClientReward)
	return tblClientReward
}

func GetClientReward(id int64, option ...consulconfig.Option) *ClientReward {
	fitKey := fixKeyClientReward(option...)
	store, ok := storeClientReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ClientReward)
		if ok {
			return storeMap[id]
		}
	}
	lockClientReward.Lock()
	defer lockClientReward.Unlock()
	store, ok = storeClientReward.Load(fitKey)
	if ok {
		tblClientReward, ok := store.(*ClientReward)
		if ok {
			return tblClientReward
		}
	}
	tblClientReward := make(map[int64]*ClientReward)
	client_reward_str, err := consulconfig.GetInstance().GetConfig(strClientReward, option...)
	if client_reward_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(client_reward_str), &tblClientReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "client_reward", errUnmarshal)
		return nil
	}
	storeClientReward.Store(fitKey, tblClientReward)
	return tblClientReward[id]
}

func LoadAllClientRewardCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strClientReward, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ClientReward", successChannels)
	return nil
}
