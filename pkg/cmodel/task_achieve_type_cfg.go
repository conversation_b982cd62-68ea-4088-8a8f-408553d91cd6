// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type TaskAchieveType struct {
	Id         int64  `json:"id"`
	Name       string `json:"name"`
	Mark       string `json:"mark"`
	Languageid int64  `json:"languageid"`
	Icon       string `json:"icon"`
}

var lockTaskAchieveType sync.RWMutex
var storeTaskAchieveType sync.Map
var strTaskAchieveType string = "task_achieve_type"

func InitTaskAchieveTypeCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTaskAchieveType, watchTaskAchieveTypeFunc)
	return LoadAllTaskAchieveTypeCfg()
}

func fixKeyTaskAchieveType(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTaskAchieveType)
}
func watchTaskAchieveTypeFunc(key string, js string) {
	mapTaskAchieveType := make(map[int64]*TaskAchieveType)
	errUnmarshal := json.Unmarshal([]byte(js), &mapTaskAchieveType)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTaskAchieveType.Store(key, mapTaskAchieveType)
}

func GetAllTaskAchieveType(option ...consulconfig.Option) map[int64]*TaskAchieveType {
	fitKey := fixKeyTaskAchieveType(option...)
	store, ok := storeTaskAchieveType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskAchieveType)
		if ok {
			return storeMap
		}
	}
	lockTaskAchieveType.Lock()
	defer lockTaskAchieveType.Unlock()
	store, ok = storeTaskAchieveType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskAchieveType)
		if ok {
			return storeMap
		}
	}
	tblTaskAchieveType := make(map[int64]*TaskAchieveType)
	task_achieve_type_str, err := consulconfig.GetInstance().GetConfig(strTaskAchieveType, option...)
	if task_achieve_type_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_achieve_type_str), &tblTaskAchieveType)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_achieve_type", errUnmarshal)
		return nil
	}
	storeTaskAchieveType.Store(fitKey, tblTaskAchieveType)
	return tblTaskAchieveType
}

func GetTaskAchieveType(id int64, option ...consulconfig.Option) *TaskAchieveType {
	fitKey := fixKeyTaskAchieveType(option...)
	store, ok := storeTaskAchieveType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskAchieveType)
		if ok {
			return storeMap[id]
		}
	}
	lockTaskAchieveType.Lock()
	defer lockTaskAchieveType.Unlock()
	store, ok = storeTaskAchieveType.Load(fitKey)
	if ok {
		tblTaskAchieveType, ok := store.(*TaskAchieveType)
		if ok {
			return tblTaskAchieveType
		}
	}
	tblTaskAchieveType := make(map[int64]*TaskAchieveType)
	task_achieve_type_str, err := consulconfig.GetInstance().GetConfig(strTaskAchieveType, option...)
	if task_achieve_type_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_achieve_type_str), &tblTaskAchieveType)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_achieve_type", errUnmarshal)
		return nil
	}
	storeTaskAchieveType.Store(fitKey, tblTaskAchieveType)
	return tblTaskAchieveType[id]
}

func LoadAllTaskAchieveTypeCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strTaskAchieveType, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "TaskAchieveType", successChannels)
	return nil
}
