package applestore

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/registry"
)

// Credentials Apple SDK 凭证配置
type Credentials struct {
	PkgName  string `json:"pkg_name" mapstructure:"pkg_name"`
	ClientID string `json:"client_id" mapstructure:"client_id"`
	KeyID    string `json:"key_id" mapstructure:"key_id"`
	TeamID   string `json:"team_id" mapstructure:"team_id"`
	Secret   string `json:"secret" mapstructure:"secret"` // Private key
}

var CredsRegistry = registry.New[Credentials]()
