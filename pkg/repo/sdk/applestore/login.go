package applestore

import (
	"context"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/applestore/lib"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/sdkvar"
)

type AppleLogin struct {
}

func (ap *AppleLogin) GetUserAccount(channelID commonPB.CHANNEL_TYPE, accessToken string, code string) (*sdkvar.SdkUserInfo, error) {
	// 获取凭证配置
	creds := CredsRegistry.Get(channelID)

	secret, err := lib.GenerateClientSecret(creds.Secret, creds.TeamID, creds.PkgName, creds.KeyID)
	if err != nil {
		return nil, err
	}

	// Generate a new validation client
	client := lib.New()

	vReq := lib.AppValidationTokenRequest{
		ClientID:     creds.PkgName,
		ClientSecret: secret,
		Code:         code,
	}

	var resp lib.ValidationResponse

	// Do the verification
	err = client.VerifyAppToken(context.Background(), vReq, &resp)
	if err != nil {
		fmt.Println("error verifying: " + err.Error())
		return nil, err
	}

	if resp.Error != "" {
		fmt.Printf("apple returned an error: %s - %s\n", resp.Error, resp.ErrorDescription)
		return nil, fmt.Errorf("apple rsp err:%s", resp.Error)
	}

	unique, err := lib.GetUniqueID(resp.IDToken)
	if err != nil {
		fmt.Println("failed to get unique ID: " + err.Error())
		return nil, err
	}

	info := &sdkvar.SdkUserInfo{
		OpenID:     unique,
		AppleToken: resp.RefreshToken,
	}

	return info, nil
}
