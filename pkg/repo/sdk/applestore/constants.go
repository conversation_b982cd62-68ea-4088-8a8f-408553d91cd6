package applestore

const (
	HostSandBox = "https://sandbox.itunes.apple.com/verifyReceipt"
	HostRelease = "https://buy.itunes.apple.com/verifyReceipt"
)

// 服务器二次验证代码
const (
	Err21000 = 21000 // * 21000 App Store不能读取你提供的JSON对象
	Err21002 = 21002 // * 21002 receipt-data域的数据有问题
	Err21003 = 21003 // * 21003 receipt无法通过验证
	Err21004 = 21004 // * 21004 提供的shared secret不匹配你账号中的shared secret
	Err21005 = 21005 // * 21005 receipt服务器当前不可用
	Err21006 = 21006 // * 21006 receipt合法，但是订阅已过期。服务器接收到这个状态码时，receipt数据仍然会解码并一起发送
	Err21007 = 21007 // * 21007 receipt是Sandbox receipt，但却发送至生产系统的验证服务
	Err21008 = 21008 // * 21008 receipt是生产receipt，但却发送至Sandbox环境的验证服务
)
