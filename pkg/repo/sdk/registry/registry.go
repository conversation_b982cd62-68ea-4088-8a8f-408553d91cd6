package registry

import (
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// Registry 通用的基于渠道的凭证注册表，使用泛型复用存取逻辑
type Registry[T any] struct {
	mu    sync.RWMutex
	creds map[commonPB.CHANNEL_TYPE]*T
}

// New 创建一个新的泛型注册表
func New[T any]() *Registry[T] {
	return &Registry[T]{
		creds: make(map[commonPB.CHANNEL_TYPE]*T),
	}
}

// Set 为指定渠道设置凭证
func (r *Registry[T]) Set(channelID commonPB.CHANNEL_TYPE, cred *T) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.creds[channelID] = cred
}

// Get 获取指定渠道的凭证
func (r *Registry[T]) Get(channelID commonPB.CHANNEL_TYPE) *T {
	r.mu.RLock()
	defer r.mu.RUnlock()
	if cred, ok := r.creds[channelID]; ok {
		return cred
	}
	var zero T
	return &zero
}
