package sdk

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/applestore"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/facebook"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/google"
)

// Credentials 所有SDK的凭证配置
type Credentials struct {
	Apple    *applestore.Credentials `json:"apple,omitempty" mapstructure:"apple"`
	Facebook *facebook.Credentials   `json:"facebook,omitempty" mapstructure:"facebook"`
	Google   *google.Credentials     `json:"google,omitempty" mapstructure:"google"`
}

// SetCredentials 为指定渠道设置SDK凭证
func SetCredentials(channelID commonPB.CHANNEL_TYPE, creds *Credentials) {
	if creds.Apple != nil {
		applestore.CredsRegistry.Set(channelID, creds.Apple)
	}
	if creds.Facebook != nil {
		facebook.CredsRegistry.Set(channelID, creds.Facebook)
	}
	if creds.Google != nil {
		google.CredsRegistry.Set(channelID, creds.Google)
	}
}
