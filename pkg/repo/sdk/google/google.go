package google

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/sdkvar"
	"git.keepfancy.xyz/back-end/frameworks/lib/httpx"
	"github.com/sirupsen/logrus"
	"golang.org/x/oauth2"
	gamesv1 "google.golang.org/api/games/v1"
	"google.golang.org/api/option"
)

type GoogleLogin struct {
}

func (g *GoogleLogin) GetUserAccount(channelID commonPB.CHANNEL_TYPE, accessToken string, code string) (*sdkvar.SdkUserInfo, error) {
	logrus.Infof("GetUserAccount called with: accessToken=%s, authorization=%s",
		maskToken(accessToken), maskToken(code))

	ctx := context.Background()

	// 如果提供了授权码而不是访问令牌，则使用授权码获取访问令牌
	if accessToken == "" && code != "" {
		var err error
		logrus.Info("Using auth code to get access token")
		accessToken, err = exchangeCodeForToken(channelID, code)
		if err != nil {
			logrus.Errorf("google exchange code for token error: %v", err)
			return nil, err
		}
		logrus.Infof("Successfully exchanged auth code for access token: %s", maskToken(accessToken))
	}

	// 创建带有访问令牌的HTTP客户端
	tokenSource := oauth2.StaticTokenSource(&oauth2.Token{AccessToken: accessToken})
	httpClient := oauth2.NewClient(ctx, tokenSource)

	// 获取Play Games玩家信息
	playGamesInfo, err := getPlayGamesPlayerInfoViaAPI(ctx, httpClient)
	if err != nil {
		logrus.Errorf("getPlayGamesPlayerInfoViaAPI error: %v", err)
		return nil, err
	}

	// 从Play Games信息创建用户账户
	userInfo := &sdkvar.SdkUserInfo{
		Name:   playGamesInfo.DisplayName,
		Avatar: playGamesInfo.AvatarImageUrl,
		OpenID: playGamesInfo.PlayerId,
	}
	return userInfo, nil
}

// getPlayGamesPlayerInfoViaAPI 使用官方Google Games API获取玩家信息
func getPlayGamesPlayerInfoViaAPI(ctx context.Context, client *http.Client) (*gamesv1.Player, error) {
	// 使用Google官方Games API获取玩家信息
	gamesService, err := gamesv1.NewService(ctx, option.WithHTTPClient(client))
	if err != nil {
		return nil, fmt.Errorf("creating games service: %v", err)
	}

	// 获取当前玩家信息
	player, err := gamesService.Players.Get("me").Do()
	if err != nil {
		return nil, fmt.Errorf("getting player info: %v", err)
	}

	return player, nil
}

// maskToken 对令牌进行掩码处理以便安全记录
func maskToken(token string) string {
	if token == "" {
		return ""
	}
	if len(token) <= 8 {
		return "****"
	}
	return token[:4] + "..." + token[len(token)-4:]
}

// exchangeCodeForToken 使用授权码交换访问令牌
func exchangeCodeForToken(channelID commonPB.CHANNEL_TYPE, authCode string) (string, error) {
	// 获取凭证配置
	creds := CredsRegistry.Get(channelID)

	// 使用直接HTTP请求方式交换令牌
	// 构建请求体
	data := map[string]string{
		"code":          authCode,
		"client_id":     creds.ClientId,
		"client_secret": creds.ClientSecret,
		"grant_type":    AuthorizationCode,
		"redirect_uri":  RedirectURI,
	}

	// 添加日志记录以便调试
	logrus.Infof("Exchanging auth code directly with params: client_id=%s, redirect_uri=%s",
		maskToken(creds.ClientId), RedirectURI)

	// 使用httpx中间件发送POST请求
	responseData, err := httpx.PostBackJsonByMap(TokenURL, data)
	if err != nil {
		logrus.Errorf("Token exchange HTTP error: %v", err)
		return "", protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}

	// 记录响应内容（注意保护敏感信息）
	logrus.Debugf("Token exchange response: %s", maskSensitiveInfo(string(responseData)))

	// 解析响应
	var tokenResp struct {
		AccessToken string `json:"access_token"`
		IdToken     string `json:"id_token"`
		ExpiresIn   int    `json:"expires_in"`
		TokenType   string `json:"token_type"`
		Error       string `json:"error"`
		ErrorDesc   string `json:"error_description"`
	}

	if err = json.Unmarshal(responseData, &tokenResp); err != nil {
		logrus.Errorf("Error parsing token response: %v", err)
		return "", protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}

	// 检查错误
	if tokenResp.Error != "" {
		logrus.Errorf("Token exchange error: %s - %s", tokenResp.Error, tokenResp.ErrorDesc)
		return "", protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}

	// 检查访问令牌是否为空
	if tokenResp.AccessToken == "" {
		logrus.Error("Received empty access token")
		return "", protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}

	logrus.Infof("Successfully exchanged auth code for access token. Expires in: %d seconds",
		tokenResp.ExpiresIn)

	return tokenResp.AccessToken, nil
}

// maskSensitiveInfo 对响应中的敏感信息进行掩码处理
func maskSensitiveInfo(jsonStr string) string {
	// 简单替换访问令牌和ID令牌
	jsonStr = regexp.MustCompile(`"access_token":"[^"]+"`).ReplaceAllString(jsonStr, `"access_token":"***"`)
	jsonStr = regexp.MustCompile(`"id_token":"[^"]+"`).ReplaceAllString(jsonStr, `"id_token":"***"`)
	return jsonStr
}
