package facebook

type Error struct {
	Code      int32  `json:"code"`
	Message   string `json:"message"`
	Type      string `json:"type"`
	FBTraceID string `json:"fbtrace_id"`
}

type UserInfo struct {
	Error Error   `json:"error"`
	Name  string  `json:"name"`
	ID    string  `json:"id"`
	Pic   Picture `json:"picture"`
	Email string  `json:"email"`
}

type Picture struct {
	Data struct {
		Height       int32  `json:"height"`
		Width        int32  `json:"width"`
		IsSilhouette bool   `json:"is_silhouette"`
		URL          string `json:"url"`
	} `json:"data"`
}

type PictureRsp struct {
	Error Error `json:"error"`
	Picture
}

type FriendRsp struct {
	Error   Error        `json:"error"`
	Data    []FriendItem `json:"data"`    //好友信息列表
	Paging  Paging       `json:"paging"`  //分页信息
	Summary Summary      `json:"summary"` //记录总数
}

type FriendItem struct {
	ID      string  `json:"id"`      //facebook id
	Name    string  `json:"name"`    //昵称
	Picture Picture `json:"picture"` //头像信息
}

type Cursors struct {
	Before string `json:"before"` //上一个游标
	After  string `json:"after"`  //下一个游标
}

type Paging struct {
	Cursors  Cursors `json:"cursors"`  //游标信息
	Previous string  `json:"previous"` //上一页地址，如果为空则是第一页
	Next     string  `json:"next"`     //下一页地址，如果为空则是最后一页
}

type Summary struct {
	TotalCount int32 `json:"total_count"` //记录总数
}

type FinderRsp struct {
	Error  Error        `json:"error"`
	Data   []FinderItem `json:"data"`   //好友信息列表
	Paging Paging       `json:"paging"` //分页信息
}

type FinderItem struct {
	Application ApplicationInfo `json:"application"`
	CreatedTime string          `json:"created_time"`
	From        FromInfo        `json:"from"`
	Message     string          `json:"message"`
	To          ToInfo          `json:"to"`
	ID          string          `json:"id"`
}

type FromInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type ToInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type ApplicationInfo struct {
	Name string `json:"name"`
	Id   string `json:"id"`
}

// Facebook受限登录相关结构体

// LimitedLoginResult 受限登录验证结果
type LimitedLoginResult struct {
	TokenMasked       string                 `json:"token_masked"`                   // 掩码后的token
	TokenLen          int                    `json:"token_len"`                      // token长度
	VerifiedBy        string                 `json:"verified_by"`                    // 验证方式 (HS256/RS256/traditional_access_token)
	Payload           map[string]interface{} `json:"payload"`                        // JWT payload或用户信息
	HS256Error        string                 `json:"hs256_error,omitempty"`          // HS256验证错误
	RS256Attempt      bool                   `json:"rs256_attempt"`                  // 是否尝试了RS256
	RS256OK           bool                   `json:"rs256_ok"`                       // RS256是否成功
	RS256Info         interface{}            `json:"rs256_info,omitempty"`           // RS256验证信息
	JWKSURLFromOpenID string                 `json:"jwks_url_from_openid,omitempty"` // 从OpenID配置获取的JWKS URL
	Error             string                 `json:"error,omitempty"`                // 错误信息
}

// OpenIDConfiguration OpenID配置响应
type OpenIDConfiguration struct {
	Issuer                           string   `json:"issuer"`
	AuthorizationEndpoint            string   `json:"authorization_endpoint"`
	TokenEndpoint                    string   `json:"token_endpoint"`
	UserinfoEndpoint                 string   `json:"userinfo_endpoint"`
	JWKSUri                          string   `json:"jwks_uri"`
	ResponseTypesSupported           []string `json:"response_types_supported"`
	SubjectTypesSupported            []string `json:"subject_types_supported"`
	IdTokenSigningAlgValuesSupported []string `json:"id_token_signing_alg_values_supported"`
}

// JWKSResponse JWKS响应
type JWKSResponse struct {
	Keys []JWK `json:"keys"`
}

// JWK JSON Web Key
type JWK struct {
	Kty string `json:"kty"` // Key Type
	Use string `json:"use"` // Public Key Use
	Kid string `json:"kid"` // Key ID
	N   string `json:"n"`   // Modulus
	E   string `json:"e"`   // Exponent
	Alg string `json:"alg"` // Algorithm
}
