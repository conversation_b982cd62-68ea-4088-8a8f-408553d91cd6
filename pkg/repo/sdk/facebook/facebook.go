package facebook

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/sdkvar"
	"git.keepfancy.xyz/back-end/frameworks/lib/httpx"
	"github.com/golang-jwt/jwt"
	"github.com/sirupsen/logrus"
)

type FBLogin struct {
}

func (fb *FBLogin) GetUserAccount(channelID commonPB.CHANNEL_TYPE, accessToken string, code string) (*sdkvar.SdkUserInfo, error) {
	logrus.Infof("GetUserAccount called with: accessToken=%s, authorization=%s",
		maskToken(accessToken), maskToken(code))

	// 判断token类型并获取用户信息
	var fbUserInfo *UserInfo
	var err error

	if isJWTToken(accessToken) {
		// JWT token (受限登录)
		logrus.Infof("Detected JWT token, using limited login verification")
		fbUserInfo, err = getUserInfoFromJWT(channelID, accessToken)
	} else {
		// 传统access token
		logrus.Infof("Detected access token, using traditional verification")
		fbUserInfo, err = getUserInfo(channelID, accessToken)
	}

	if err != nil {
		logrus.Errorf("facebook get user info error: %v", err)
		return nil, err
	}

	userInfo := &sdkvar.SdkUserInfo{
		Name:   fbUserInfo.Name,
		Avatar: fbUserInfo.Pic.Data.URL,
		OpenID: fbUserInfo.ID,
		Email:  fbUserInfo.Email,
	}

	logrus.Infof("Successfully got user info: name=%s, openid=%s", userInfo.Name, userInfo.OpenID)
	return userInfo, nil
}

// getUserInfo 获取facebook信息
func getUserInfo(channelID commonPB.CHANNEL_TYPE, accessToken string) (userInfo *UserInfo, err error) {
	params := url.Values{}
	params.Set("access_token", accessToken)

	// 生成 app secret proof
	appSecretProof := GenerateAppSecretProof(channelID, accessToken)
	if appSecretProof == "" {
		logrus.Errorf("failed to generate app secret proof")
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}
	params.Set("appsecret_proof", appSecretProof)
	params.Set("fields", "id,name,picture,email")

	logrus.Debugf("facebook get user info request: url=%s, params=%v", UserInfoURL, params)

	data, err := httpx.GetBackJson(UserInfoURL, params)
	if err != nil {
		logrus.Errorf("facebook get user info http error: %v", err)
		return nil, err
	}

	logrus.Debugf("facebook get user info response: %s", maskSensitiveInfo(string(data)))

	userInfo = &UserInfo{}
	if err = json.Unmarshal(data, userInfo); err != nil {
		logrus.Errorf("facebook unmarshal user info error: %v", err)
		return nil, err
	}

	if userInfo.Error.Code != 0 {
		logrus.Errorf("facebook get user info error: code=%d, message=%s, type=%s",
			userInfo.Error.Code, userInfo.Error.Message, userInfo.Error.Type)
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}

	return
}

// maskToken 对令牌进行掩码处理以便安全记录
func maskToken(token string) string {
	if token == "" {
		return ""
	}
	if len(token) <= 8 {
		return "****"
	}
	return token[:4] + "..." + token[len(token)-4:]
}

// maskSensitiveInfo 对响应中的敏感信息进行掩码处理
func maskSensitiveInfo(jsonStr string) string {
	// 简单替换访问令牌
	jsonStr = regexp.MustCompile(`"access_token":"[^"]+"`).ReplaceAllString(jsonStr, `"access_token":"***"`)
	return jsonStr
}

// GetFriends 获取好友信息
// @accessToken 用户fbtoken
// @limit 分页数量
// @after 下一页请求地址
func GetFriends(accessToken string, limit int64, after string) (frdRsp *FriendRsp, err error) {
	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("limit", strconv.Itoa(int(limit)))
	params.Set("after", after)
	data, err := httpx.GetBackJson(FriendURL, params)
	if err != nil {
		return
	}
	frdRsp = &FriendRsp{}
	if err = json.Unmarshal(data, frdRsp); err != nil {
		return
	}

	if frdRsp.Error.Code != 0 {
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}
	return
}

// GetFinder 获取邀请信息
// @accessToken 用户token
func GetFinder(accessToken string) (fidRsp *FinderRsp, err error) {
	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("limit", GetFinderLimit)
	data, err := httpx.GetBackJson(FinderURL, params)
	if err != nil {
		return
	}
	fidRsp = &FinderRsp{}
	if err = json.Unmarshal(data, fidRsp); err != nil {
		return
	}

	if fidRsp.Error.Code != 0 {
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}
	return
}

// isJWTToken 判断是否为JWT token（包含两个点的格式）
func isJWTToken(token string) bool {
	return strings.Count(token, ".") == 2
}

// getUserInfoFromJWT 从JWT token获取用户信息（Facebook受限登录）
func getUserInfoFromJWT(channelID commonPB.CHANNEL_TYPE, jwtToken string) (*UserInfo, error) {
	// 获取凭证配置
	creds := CredsRegistry.Get(channelID)

	// 验证JWT token
	payload, err := verifyJWTToken(jwtToken, creds.AppSecret)
	if err != nil {
		return nil, fmt.Errorf("JWT verification failed: %w", err)
	}

	// 从JWT payload构造UserInfo
	userInfo := &UserInfo{}

	// 提取用户ID
	if sub, ok := payload["sub"].(string); ok {
		userInfo.ID = sub
	} else {
		return nil, fmt.Errorf("missing or invalid 'sub' claim in JWT")
	}

	// 提取用户名
	if name, ok := payload["name"].(string); ok {
		userInfo.Name = name
	}

	// 提取邮箱
	if email, ok := payload["email"].(string); ok {
		userInfo.Email = email
	}

	// 提取头像
	if picture, ok := payload["picture"].(string); ok {
		userInfo.Pic.Data.URL = picture
	}

	logrus.Debugf("Extracted user info from JWT: id=%s, name=%s", userInfo.ID, userInfo.Name)
	return userInfo, nil
}

// verifyJWTToken 验证JWT token（支持HS256和RS256）
func verifyJWTToken(tokenString, appSecret string) (map[string]interface{}, error) {
	// 首先尝试HS256验证（使用app secret）
	if appSecret != "" {
		if payload, err := verifyHS256Token(tokenString, appSecret); err == nil {
			logrus.Debugf("JWT verified using HS256")
			return payload, nil
		} else {
			logrus.Debugf("HS256 verification failed: %v", err)
		}
	}

	// 尝试RS256验证（使用Facebook公钥）
	payload, err := verifyRS256Token(tokenString)
	if err != nil {
		return nil, fmt.Errorf("both HS256 and RS256 verification failed: %w", err)
	}

	logrus.Debugf("JWT verified using RS256")
	return payload, nil
}

// verifyHS256Token 使用HS256算法验证JWT token
func verifyHS256Token(tokenString, secret string) (map[string]interface{}, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(secret), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, fmt.Errorf("token is not valid")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("failed to extract claims")
	}

	// 转换为map[string]interface{}
	payload := make(map[string]interface{})
	for k, v := range claims {
		payload[k] = v
	}

	return payload, nil
}

// verifyRS256Token 使用RS256算法验证JWT token（使用Facebook公钥）
func verifyRS256Token(tokenString string) (map[string]interface{}, error) {
	// 获取Facebook的JWKS
	jwks, err := fetchFacebookJWKS()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch Facebook JWKS: %w", err)
	}

	// 解析token获取kid
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	kid, ok := token.Header["kid"].(string)
	if !ok {
		return nil, fmt.Errorf("missing kid in token header")
	}

	// 查找对应的公钥
	publicKey, err := getPublicKeyFromJWKS(jwks, kid)
	if err != nil {
		return nil, fmt.Errorf("failed to get public key: %w", err)
	}

	// 验证token
	token, err = jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return publicKey, nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, fmt.Errorf("token is not valid")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("failed to extract claims")
	}

	// 转换为map[string]interface{}
	payload := make(map[string]interface{})
	for k, v := range claims {
		payload[k] = v
	}

	return payload, nil
}

// fetchFacebookJWKS 获取Facebook的JWKS
func fetchFacebookJWKS() (*JWKSResponse, error) {
	client := &http.Client{
		Timeout: time.Duration(DefaultHTTPTimeout) * time.Second,
	}

	resp, err := client.Get(DefaultJWKSURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("JWKS request failed with status: %d", resp.StatusCode)
	}

	var jwks JWKSResponse
	if err := json.NewDecoder(resp.Body).Decode(&jwks); err != nil {
		return nil, err
	}

	return &jwks, nil
}

// getPublicKeyFromJWKS 从JWKS中获取指定kid的公钥
func getPublicKeyFromJWKS(jwks *JWKSResponse, kid string) (interface{}, error) {
	for _, key := range jwks.Keys {
		if key.Kid == kid && key.Kty == "RSA" {
			return parseRSAPublicKey(key)
		}
	}
	return nil, fmt.Errorf("public key not found for kid: %s", kid)
}

// parseRSAPublicKey 解析RSA公钥
func parseRSAPublicKey(jwk JWK) (interface{}, error) {
	// 简化实现：对于RS256验证，我们可以使用第三方库或者先实现HS256验证
	// 这里暂时返回错误，提示使用HS256验证
	return nil, fmt.Errorf("RS256 verification not fully implemented, please use HS256 with app_secret")
}
