package facebook

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/sdkvar"
	"git.keepfancy.xyz/back-end/frameworks/lib/httpx"
	"github.com/golang-jwt/jwt"
	"github.com/sirupsen/logrus"
)

type FBLogin struct {
}

func (fb *FBLogin) GetUserAccount(channelID commonPB.CHANNEL_TYPE, accessToken string, code string) (*sdkvar.SdkUserInfo, error) {
	logrus.Infof("GetUserAccount called with: accessToken=%s, authorization=%s",
		maskToken(accessToken), maskToken(code))

	// 使用统一的token验证逻辑
	result, err := VerifyFacebookToken(channelID, accessToken)
	if err != nil {
		logrus.Errorf("facebook token verification failed: %v", err)
		return nil, err
	}

	// 从验证结果中提取用户信息
	fbUserInfo, err := extractUserInfoFromVerificationResult(result)
	if err != nil {
		logrus.Errorf("failed to extract user info from verification result: %v", err)
		return nil, err
	}

	userInfo := &sdkvar.SdkUserInfo{
		Name:   fbUserInfo.Name,
		Avatar: fbUserInfo.Pic.Data.URL,
		OpenID: fbUserInfo.ID,
		Email:  fbUserInfo.Email,
	}

	logrus.Infof("Successfully got user info via %s: name=%s, openid=%s",
		result.VerifiedBy, userInfo.Name, userInfo.OpenID)
	return userInfo, nil
}

// getUserInfo 获取facebook信息
func getUserInfo(channelID commonPB.CHANNEL_TYPE, accessToken string) (userInfo *UserInfo, err error) {
	params := url.Values{}
	params.Set("access_token", accessToken)

	// 生成 app secret proof
	appSecretProof := GenerateAppSecretProof(channelID, accessToken)
	if appSecretProof == "" {
		logrus.Errorf("failed to generate app secret proof")
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}
	params.Set("appsecret_proof", appSecretProof)
	params.Set("fields", "id,name,picture,email")

	logrus.Debugf("facebook get user info request: url=%s, params=%v", UserInfoURL, params)

	data, err := httpx.GetBackJson(UserInfoURL, params)
	if err != nil {
		logrus.Errorf("facebook get user info http error: %v", err)
		return nil, err
	}

	logrus.Debugf("facebook get user info response: %s", maskSensitiveInfo(string(data)))

	userInfo = &UserInfo{}
	if err = json.Unmarshal(data, userInfo); err != nil {
		logrus.Errorf("facebook unmarshal user info error: %v", err)
		return nil, err
	}

	if userInfo.Error.Code != 0 {
		logrus.Errorf("facebook get user info error: code=%d, message=%s, type=%s",
			userInfo.Error.Code, userInfo.Error.Message, userInfo.Error.Type)
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}

	return
}

// maskToken 对令牌进行掩码处理以便安全记录
func maskToken(token string) string {
	if token == "" {
		return ""
	}
	if len(token) <= 8 {
		return "****"
	}
	return token[:4] + "..." + token[len(token)-4:]
}

// maskSensitiveInfo 对响应中的敏感信息进行掩码处理
func maskSensitiveInfo(jsonStr string) string {
	// 简单替换访问令牌
	jsonStr = regexp.MustCompile(`"access_token":"[^"]+"`).ReplaceAllString(jsonStr, `"access_token":"***"`)
	return jsonStr
}

// GetFriends 获取好友信息
// @accessToken 用户fbtoken
// @limit 分页数量
// @after 下一页请求地址
func GetFriends(accessToken string, limit int64, after string) (frdRsp *FriendRsp, err error) {
	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("limit", strconv.Itoa(int(limit)))
	params.Set("after", after)
	data, err := httpx.GetBackJson(FriendURL, params)
	if err != nil {
		return
	}
	frdRsp = &FriendRsp{}
	if err = json.Unmarshal(data, frdRsp); err != nil {
		return
	}

	if frdRsp.Error.Code != 0 {
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}
	return
}

// GetFinder 获取邀请信息
// @accessToken 用户token
func GetFinder(accessToken string) (fidRsp *FinderRsp, err error) {
	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("limit", GetFinderLimit)
	data, err := httpx.GetBackJson(FinderURL, params)
	if err != nil {
		return
	}
	fidRsp = &FinderRsp{}
	if err = json.Unmarshal(data, fidRsp); err != nil {
		return
	}

	if fidRsp.Error.Code != 0 {
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}
	return
}

// VerifyFacebookToken 统一的Facebook token验证函数（公共逻辑）
// 支持JWT token（HS256/RS256）和传统access token
func VerifyFacebookToken(channelID commonPB.CHANNEL_TYPE, token string) (*LimitedLoginResult, error) {
	if token == "" {
		return nil, fmt.Errorf("token is empty")
	}

	// 清理token（移除引号）
	token = strings.Trim(token, `"'`)

	// 初始化结果
	result := &LimitedLoginResult{
		TokenMasked: maskToken(token),
		TokenLen:    len(token),
	}

	// 获取凭证配置
	creds := CredsRegistry.Get(channelID)

	// 判断token类型
	isJWTLike := strings.Count(token, ".") >= 2
	isAccessTokenLike := strings.HasPrefix(token, "EAA")

	// 策略1: 如果是JWT格式，尝试HS256验证
	if isJWTLike && creds.ClientSecret != "" {
		if success, err := tryHS256Verification(token, creds.ClientSecret, result); success {
			return result, nil
		} else if err != nil {
			logrus.Debugf("HS256 verification failed: %v", err)
			result.HS256Error = err.Error()
		}
	}

	// 策略2: 如果是JWT格式，尝试RS256验证（需要JWKS）
	if isJWTLike {
		if success, err := tryRS256Verification(token, result); success {
			return result, nil
		} else if err != nil {
			logrus.Debugf("RS256 verification failed: %v", err)
		}
	}

	// 策略3: 如果是传统access token，使用现有的getUserInfo逻辑
	if isAccessTokenLike {
		// 对于传统access token，直接使用现有的验证逻辑
		userInfo, err := getUserInfo(channelID, token)
		if err != nil {
			result.Error = fmt.Sprintf("traditional access token verification failed: %v", err)
			return result, err
		}

		result.VerifiedBy = "traditional_access_token"
		result.Payload = map[string]interface{}{
			"sub":     userInfo.ID,
			"name":    userInfo.Name,
			"email":   userInfo.Email,
			"picture": userInfo.Pic.Data.URL,
		}
		return result, nil
	}

	// 所有验证方法都失败
	result.Error = "no verification method succeeded (provide app_secret for HS256 or use valid JWT/access token)"
	return result, fmt.Errorf("all token verification methods failed")
}

// tryHS256Verification 尝试HS256验证
func tryHS256Verification(token, appSecret string, result *LimitedLoginResult) (bool, error) {
	payload, err := verifyHS256TokenWithDetails(token, appSecret)
	if err != nil {
		return false, err
	}

	result.VerifiedBy = "HS256"
	result.Payload = payload
	return true, nil
}

// tryRS256Verification 尝试RS256验证
func tryRS256Verification(token string, result *LimitedLoginResult) (bool, error) {
	result.RS256Attempt = true

	// 获取JWKS URL
	jwksURL, err := getOpenIDJWKSURL()
	if err != nil {
		result.RS256Info = fmt.Sprintf("Failed to get JWKS URL: %v", err)
		return false, err
	}

	result.JWKSURLFromOpenID = jwksURL

	// 验证RS256 token
	payload, err := verifyRS256Token(token)
	if err != nil {
		result.RS256Info = err.Error()
		return false, err
	}

	result.RS256OK = true
	result.RS256Info = "RS256 verification successful"
	result.VerifiedBy = "RS256"
	result.Payload = payload

	return true, nil
}

// extractUserInfoFromVerificationResult 从验证结果中提取用户信息
func extractUserInfoFromVerificationResult(result *LimitedLoginResult) (*UserInfo, error) {
	userInfo := &UserInfo{}

	// 从payload中提取用户信息
	if result.Payload != nil {
		if sub, ok := result.Payload["sub"].(string); ok {
			userInfo.ID = sub
		}
		if name, ok := result.Payload["name"].(string); ok {
			userInfo.Name = name
		}
		if email, ok := result.Payload["email"].(string); ok {
			userInfo.Email = email
		}
		if picture, ok := result.Payload["picture"].(string); ok {
			userInfo.Pic.Data.URL = picture
		}
	}

	if userInfo.ID == "" {
		return nil, fmt.Errorf("failed to extract user ID from verification result")
	}

	return userInfo, nil
}

// getOpenIDJWKSURL 获取Facebook OpenID配置中的JWKS URL
func getOpenIDJWKSURL() (string, error) {
	client := &http.Client{Timeout: time.Duration(DefaultJWKSTimeout) * time.Second}

	resp, err := client.Get(OpenIDConfigURL)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("OpenID config request failed with status: %d", resp.StatusCode)
	}

	var config OpenIDConfiguration
	if err := json.NewDecoder(resp.Body).Decode(&config); err != nil {
		return "", err
	}

	if config.JWKSUri == "" {
		return DefaultJWKSURL, nil // 使用默认JWKS URL
	}

	return config.JWKSUri, nil
}

// isJWTToken 判断是否为JWT token（包含两个点的格式）
func isJWTToken(token string) bool {
	return strings.Count(token, ".") == 2
}

// verifyHS256TokenWithDetails 使用HS256算法验证token（支持signed_request和JWT）
func verifyHS256TokenWithDetails(tokenString, secret string) (map[string]interface{}, error) {
	parts := strings.Split(tokenString, ".")

	if len(parts) == 2 {
		// signed_request格式 (signature.payload)
		return verifySignedRequest(tokenString, secret)
	} else if len(parts) == 3 {
		// JWT格式 (header.payload.signature)
		return verifyJWTHS256(tokenString, secret)
	} else {
		return nil, fmt.Errorf("unknown token format: expected 2 or 3 parts, got %d", len(parts))
	}
}

// verifySignedRequest 验证Facebook signed_request
func verifySignedRequest(signedRequest, appSecret string) (map[string]interface{}, error) {
	parts := strings.Split(signedRequest, ".")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid signed_request format")
	}

	sigB64, payloadB64 := parts[0], parts[1]

	// 解码payload
	payloadBytes, err := base64URLDecode(payloadB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode payload: %w", err)
	}

	var payload map[string]interface{}
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		return nil, fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	// 解码签名
	sig, err := base64URLDecode(sigB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode signature: %w", err)
	}

	// 验证签名（两种方式）
	// 方式1: 对base64编码的payload签名
	mac1 := hmac.New(sha256.New, []byte(appSecret))
	mac1.Write([]byte(payloadB64))
	expectedSig1 := mac1.Sum(nil)

	// 方式2: 对JSON字符串签名
	mac2 := hmac.New(sha256.New, []byte(appSecret))
	mac2.Write(payloadBytes)
	expectedSig2 := mac2.Sum(nil)

	if !hmac.Equal(sig, expectedSig1) && !hmac.Equal(sig, expectedSig2) {
		return nil, fmt.Errorf("signed_request signature verification failed")
	}

	return payload, nil
}

// verifyJWTHS256 验证JWT HS256 token
func verifyJWTHS256(tokenString, secret string) (map[string]interface{}, error) {
	parts := strings.Split(tokenString, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid JWT format")
	}

	headerB64, payloadB64, sigB64 := parts[0], parts[1], parts[2]

	// 解码并验证header
	headerBytes, err := base64URLDecode(headerB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode header: %w", err)
	}

	var header map[string]interface{}
	if err := json.Unmarshal(headerBytes, &header); err != nil {
		return nil, fmt.Errorf("failed to unmarshal header: %w", err)
	}

	// 检查算法
	alg, ok := header["alg"].(string)
	if !ok {
		return nil, fmt.Errorf("missing alg in header")
	}

	algUpper := strings.ToUpper(alg)
	if algUpper != "HS256" && algUpper != "HMAC-SHA256" {
		return nil, fmt.Errorf("unsupported algorithm: %s", alg)
	}

	// 解码payload
	payloadBytes, err := base64URLDecode(payloadB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode payload: %w", err)
	}

	var payload map[string]interface{}
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		return nil, fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	// 验证签名
	data := headerB64 + "." + payloadB64
	sig, err := base64URLDecode(sigB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode signature: %w", err)
	}

	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write([]byte(data))
	expectedSig := mac.Sum(nil)

	if !hmac.Equal(sig, expectedSig) {
		return nil, fmt.Errorf("JWT signature verification failed")
	}

	return payload, nil
}

// base64URLDecode 解码base64 URL编码（添加必要的填充）
func base64URLDecode(s string) ([]byte, error) {
	// 添加必要的填充
	switch len(s) % 4 {
	case 2:
		s += "=="
	case 3:
		s += "="
	}
	return base64.URLEncoding.DecodeString(s)
}

// verifyRS256Token 使用RS256算法验证JWT token（使用Facebook公钥）
func verifyRS256Token(tokenString string) (map[string]interface{}, error) {
	// 获取Facebook的JWKS
	jwks, err := fetchFacebookJWKS()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch Facebook JWKS: %w", err)
	}

	// 解析token获取kid
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	kid, ok := token.Header["kid"].(string)
	if !ok {
		return nil, fmt.Errorf("missing kid in token header")
	}

	// 查找对应的公钥
	publicKey, err := getPublicKeyFromJWKS(jwks, kid)
	if err != nil {
		return nil, fmt.Errorf("failed to get public key: %w", err)
	}

	// 验证token
	token, err = jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return publicKey, nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, fmt.Errorf("token is not valid")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("failed to extract claims")
	}

	// 转换为map[string]interface{}
	payload := make(map[string]interface{})
	for k, v := range claims {
		payload[k] = v
	}

	return payload, nil
}

// fetchFacebookJWKS 获取Facebook的JWKS
func fetchFacebookJWKS() (*JWKSResponse, error) {
	client := &http.Client{
		Timeout: time.Duration(DefaultHTTPTimeout) * time.Second,
	}

	resp, err := client.Get(DefaultJWKSURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("JWKS request failed with status: %d", resp.StatusCode)
	}

	var jwks JWKSResponse
	if err := json.NewDecoder(resp.Body).Decode(&jwks); err != nil {
		return nil, err
	}

	return &jwks, nil
}

// getPublicKeyFromJWKS 从JWKS中获取指定kid的公钥
func getPublicKeyFromJWKS(jwks *JWKSResponse, kid string) (interface{}, error) {
	for _, key := range jwks.Keys {
		if key.Kid == kid && key.Kty == "RSA" {
			return parseRSAPublicKey(key)
		}
	}
	return nil, fmt.Errorf("public key not found for kid: %s", kid)
}

// parseRSAPublicKey 解析RSA公钥
func parseRSAPublicKey(jwk JWK) (interface{}, error) {
	// 简化实现：对于RS256验证，我们可以使用第三方库或者先实现HS256验证
	// 这里暂时返回错误，提示使用HS256验证
	return nil, fmt.Errorf("RS256 verification not fully implemented, please use HS256 with app_secret")
}
