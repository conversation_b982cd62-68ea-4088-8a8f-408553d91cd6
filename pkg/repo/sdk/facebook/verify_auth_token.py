#!/usr/bin/env python3
"""
verify_auth_token.py

验证 Facebook AuthenticationToken（signed_request / JWT）或对以 EAA 开头的 user access token 使用 /debug_token 验证。

用法示例：
  python verify_auth_token.py --token "eyJ..." --app-secret "YOUR_APP_SECRET" --app-id 123456 --check-exp --pretty
  python verify_auth_token.py --token "EAA..." --app-id 123456 --app-secret "YOUR_APP_SECRET" --use-debug

  python verify_auth_token.py --token "EAARhcM7fAMcBPSdz8TosLdFMLewO638ZB4T3HuR1wnwp4Lt2hIcoylZA29JcIbi7LBAidn2LpY59B7ieapyB6GGf6jR7HnD1KphUBcywfxQNB0cmvjksCZBZCZAuvv3tFUqa2AqM3QZCy2Vqt4eXOldU6bN8pVHjHOhT2fH8zb24ccZCK2sniOa2LZCVrVCx4fIZCkaYp3SiPEvk779YJ3NFEu5EjyZBA0iBTJbuas7vLD09mO1GAse24xhOVNkOWQ1LYFbgZDZD" --app-secret "f3205917fb373d49810673b218e4d859" --app-id 1233037041795271 

  python verify_auth_token.py --token "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" --app-secret "f3205917fb373d49810673b218e4d859" --app-id 1233037041795271 
" --app-id 1233037041795271 

如果不想在命令行传 app secret，可省略 --app-secret，脚本会交互式提示（不回显）。
注意：不要在公共日志或聊天中泄露完整 token 或 app secret。
"""#!/usr/bin/env python3
"""
verify_auth_token_rs_complete.py

支持：
 - 本地 HS256 (signed_request / JWT HS256) 验签（需要 app_secret）
 - RS256 验签（使用 JWKS URL 或 OpenID config 自动获取 jwks_uri）
 - 也可用 Graph API debug_token 验证 EAA... 类型的 user access token

用法示例：
  py -3 verify_auth_token_rs_complete.py --token "eyJ..." --jwks-url "https://www.facebook.com/.well-known/oauth/openid/jwks/"
  py -3 verify_auth_token_rs_complete.py --token "eyJ..." --jwks-url "..." --aud 123456 --iss "https://www.facebook.com"
  py -3 verify_auth_token_rs_complete.py --token "EAA..." --use-debug --app-id 123456 --app-secret "APP_SECRET"
"""
import argparse, base64, hashlib, hmac, json, time, getpass, sys
import requests

# PyJWT
try:
    import jwt
    from jwt import PyJWKClient
except Exception as e:
    print("Missing dependency: PyJWT[crypto]. Install with: py -3 -m pip install \"PyJWT[crypto]\" requests")
    sys.exit(2)

def b64url_decode(s):
    if isinstance(s, str):
        s = s.encode('utf-8')
    pad = b'=' * (-len(s) % 4)
    return base64.urlsafe_b64decode(s + pad)

def mask_token(t, head=6, tail=6):
    if not t:
        return "<empty>"
    if len(t) <= head + tail + 4:
        return t
    return t[:head] + "..." + t[-tail:]

def verify_hs_signed_token(token, app_secret):
    parts = token.split('.')
    try:
        if len(parts) == 2:
            sig_b64, payload_b64 = parts
            payload_json = b64url_decode(payload_b64).decode('utf-8')
            payload = json.loads(payload_json)
            sig = b64url_decode(sig_b64)
            mac1 = hmac.new(app_secret.encode(), payload_b64.encode(), hashlib.sha256).digest()
            mac2 = hmac.new(app_secret.encode(), payload_json.encode(), hashlib.sha256).digest()
            if hmac.compare_digest(mac1, sig) or hmac.compare_digest(mac2, sig):
                return True, payload
            return False, "HS256 signed_request signature mismatch"
        elif len(parts) == 3:
            header_b64, payload_b64, sig_b64 = parts
            header = json.loads(b64url_decode(header_b64).decode('utf-8'))
            payload = json.loads(b64url_decode(payload_b64).decode('utf-8'))
            alg = (header.get('alg') or '').upper()
            if alg not in ('HS256', 'HMAC-SHA256'):
                return False, f"HS256 attempt: unsupported alg {alg}"
            data = (header_b64 + '.' + payload_b64).encode('utf-8')
            sig = b64url_decode(sig_b64)
            mac = hmac.new(app_secret.encode(), data, hashlib.sha256).digest()
            if hmac.compare_digest(mac, sig):
                return True, payload
            return False, "HS256 JWT signature mismatch"
        else:
            return False, "HS256 attempt: unknown token format"
    except Exception as e:
        return False, f"HS256 verify exception: {e}"

def verify_rs256_with_jwks(token, jwks_url, audience=None, issuer=None, leeway=0):
    """
    Use PyJWKClient to fetch JWKS and verify RS256 token.
    audience: expected audience (e.g. your APP_ID) or None to skip aud check
    issuer: expected issuer string or None
    """
    try:
        jwk_client = PyJWKClient(jwks_url)
        signing_key = jwk_client.get_signing_key_from_jwt(token)  # may raise
        public_key = signing_key.key
        options = {"verify_aud": audience is not None}
        payload = jwt.decode(token, public_key, algorithms=["RS256"], audience=audience, issuer=issuer, options=options, leeway=leeway)
        return True, payload
    except Exception as e:
        return False, f"RS256 verify failed: {e}"

def debug_token_graph(input_token, app_id, app_secret, timeout=10):
    app_access = f"{app_id}|{app_secret}"
    url = "https://graph.facebook.com/debug_token"
    try:
        r = requests.get(url, params={"input_token": input_token, "access_token": app_access}, timeout=timeout)
        j = r.json()
    except Exception as e:
        return False, f"HTTP/parse error: {e}"
    if r.status_code != 200:
        return False, j
    return True, j.get("data", j)

def get_openid_config_jwks(openid_config_url="https://www.facebook.com/.well-known/openid-configuration"):
    try:
        r = requests.get(openid_config_url, timeout=6)
        r.raise_for_status()
        j = r.json()
        return j.get("jwks_uri")
    except Exception as e:
        return None

def parse_args():
    p = argparse.ArgumentParser()
    p.add_argument("--token", "-t", required=True)
    p.add_argument("--app-secret", "-s", help="APP_SECRET for HS256 verification or debug_token")
    p.add_argument("--app-id", "-a", help="APP_ID (used for debug_token or as audience)")
    p.add_argument("--jwks-url", "-j", help="JWKS URL for RS256 verification (optional). If omitted, will try FB openid config.")
    p.add_argument("--aud", help="Expected audience (aud) when verifying RS256 (e.g. your APP_ID)")
    p.add_argument("--iss", help="Expected issuer (iss) when verifying RS256")
    p.add_argument("--use-debug", action="store_true", help="If token looks like user access token (EAA...), call Graph API /debug_token (needs app-id+app-secret)")
    p.add_argument("--pretty", action="store_true")
    return p.parse_args()

def main():
    args = parse_args()
    token = args.token.strip()
    # remove surrounding quotes
    if (token.startswith('"') and token.endswith('"')) or (token.startswith("'") and token.endswith("'")):
        token = token[1:-1]

    result = {"token_masked": mask_token(token), "token_len": len(token)}
    is_access_like = token.startswith("EAA")

    # If use-debug explicitly or token looks like access token and we have app creds, try debug_token first
    if args.use_debug or (is_access_like and args.app_id and args.app_secret):
        ok, info = debug_token_graph(token, args.app_id, args.app_secret)
        result["debug_attempt"] = True
        result["debug_ok"] = ok
        result["debug_info"] = info
        print(json.dumps(result, indent=2 if args.pretty else None, ensure_ascii=False))
        sys.exit(0 if ok else 1)

    # Try HS256 verification if app_secret provided
    if args.app_secret:
        ok, info = verify_hs_signed_token(token, args.app_secret)
        if ok:
            result["verified_by"] = "HS256"
            result["payload"] = info
            # optional checks
            if args.app_id:
                payload_app = str(info.get("app_id") or info.get("aud") or info.get("audience") or "")
                result["app_id_matches"] = (payload_app == str(args.app_id))
            print(json.dumps(result, indent=2 if args.pretty else None, ensure_ascii=False))
            sys.exit(0)
        else:
            result["hs256_error"] = info
            # continue to try RS256

    # Try RS256: need jwks url. If not provided, attempt to fetch from FB openid-config
    jwks_url = args.jwks_url
    if not jwks_url:
        jwks_url = get_openid_config_jwks()
        if jwks_url:
            result["jwks_url_from_openid"] = jwks_url

    if jwks_url:
        ok, info = verify_rs256_with_jwks(token, jwks_url, audience=(args.aud if args.aud else None), issuer=(args.iss if args.iss else None))
        result["rs256_attempt"] = True
        result["rs256_ok"] = ok
        result["rs256_info"] = info
        if ok:
            result["verified_by"] = "RS256"
            result["payload"] = info
            print(json.dumps(result, indent=2 if args.pretty else None, ensure_ascii=False))
            sys.exit(0)
        else:
            print(json.dumps(result, indent=2 if args.pretty else None, ensure_ascii=False))
            sys.exit(1)

    # nothing worked
    result["error"] = "no verification method succeeded (provide --app-secret for HS256 or --jwks-url/FB openid config for RS256)"
    print(json.dumps(result, indent=2 if args.pretty else None, ensure_ascii=False))
    sys.exit(2)

if __name__ == "__main__":
    main()
