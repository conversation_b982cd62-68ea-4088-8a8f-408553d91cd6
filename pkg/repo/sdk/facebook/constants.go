package facebook

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

const (
	Host        = "https://graph.facebook.com" // 正确的Facebook Graph API域名
	Version     = "v19.0"                      // 使用最新的稳定版本
	URLPre      = Host + "/" + Version + "/"
	UserInfoURL = Host + "/" + Version + "/" + "me"
	FriendURL   = Host + "/" + Version + "/" + "me/friends"
	FinderURL   = Host + "/" + Version + "/" + "me/apprequests"
	PictureURL  = "/picture" // URLPre+1036709610527151 /picture

	// Facebook受限登录相关URL
	OpenIDConfigURL = "https://www.facebook.com/.well-known/openid-configuration" // OpenID配置
	DefaultJWKSURL  = "https://www.facebook.com/.well-known/oauth/openid/jwks/"   // 默认JWKS URL

	// 默认超时时间
	DefaultHTTPTimeout = 10 // 秒
	DefaultJWKSTimeout = 6  // 秒
)

const GetFinderLimit = "10" // 拉取接口的limit参数

// GenerateAppSecretProof 生成应用密钥证明
func GenerateAppSecretProof(channelID commonPB.CHANNEL_TYPE, accessToken string) string {
	// 获取凭证配置
	creds := CredsRegistry.Get(channelID)
	key := []byte(creds.ClientSecret)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(accessToken))
	return hex.EncodeToString(h.Sum(nil))
}
