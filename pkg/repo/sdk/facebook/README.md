# Facebook SDK - 受限登录支持

本SDK支持Facebook的传统登录和受限登录（Limited Login）功能。

## 功能特性

- **智能Token识别**：自动识别JWT token和传统access token
- **多种验证方式**：支持HS256、RS256和传统access token验证
- **统一接口**：使用相同的`GetUserAccount`方法处理不同类型的token
- **生产就绪**：专为生产环境设计，不依赖debug_token API

## 使用方法

### 1. 配置凭证

```go
import (
    "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/facebook"
    commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// 注册Facebook凭证
facebook.CredsRegistry.Set(commonPB.CHANNEL_TYPE_FACEBOOK, &facebook.Credentials{
    ClientSecret: "your_facebook_app_secret", // 用于JWT HS256验证
})
```

### 2. 使用统一接口获取用户信息

```go
fbLogin := &facebook.FBLogin{}

// 支持传统access token (EAA开头)
userInfo1, err := fbLogin.GetUserAccount(
    commonPB.CHANNEL_TYPE_FACEBOOK,
    "EAAd6E15hmfABO0KLr5vLIPd6c5cs6N4CTqjaqdrLRKnxMcZAFFWCAmKtSjZCVdnbkJrYAbNIo9XIbKpQL9nI9aeAnClhwNRcHHUOQnyNtqdZCBRcodaFfgjXcR3tLVBHiCFhNGvkhhnOrNeWjXZCDvfC7aZAjGPjHDUJioxYpxvNuV9h6krQ3tFP7HaEE2tmG1jifCfeLyb9wxCcdazT8deeJplTiqjG6zn1q9hYgnEZBVom5UZCkMoDaRblcEVUqkZD",
    "",
)

// 支持JWT token (受限登录)
userInfo2, err := fbLogin.GetUserAccount(
    commonPB.CHANNEL_TYPE_FACEBOOK,
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.signature_here",
    "",
)
```

### 3. 直接使用验证函数（高级用法）

```go
// 直接验证token并获取详细结果
result, err := facebook.VerifyFacebookToken(commonPB.CHANNEL_TYPE_FACEBOOK, token)
if err != nil {
    log.Printf("Token verification failed: %v", err)
    return
}

log.Printf("Token verified by: %s", result.VerifiedBy)
log.Printf("User ID: %s", result.Payload["sub"])
log.Printf("User Name: %s", result.Payload["name"])
```

## Token类型支持

### 1. 传统Access Token
- 格式：以"EAA"开头的长字符串
- 验证方式：调用Facebook Graph API获取用户信息
- 适用场景：传统Facebook登录

### 2. JWT Token (受限登录)
- 格式：三段式JWT (header.payload.signature)
- 验证方式：
  - **HS256**：使用app_secret进行HMAC验证
  - **RS256**：使用Facebook公钥进行RSA验证
- 适用场景：Facebook受限登录

### 3. Signed Request
- 格式：两段式 (signature.payload)
- 验证方式：使用app_secret进行HMAC验证
- 适用场景：Facebook Canvas应用

## 验证策略

系统会按以下顺序尝试验证：

1. **JWT格式 + HS256**：如果token是JWT格式且有app_secret，优先使用HS256验证
2. **JWT格式 + RS256**：如果HS256失败，尝试使用Facebook公钥进行RS256验证
3. **传统Access Token**：如果是EAA开头的token，使用传统Graph API验证

## 错误处理

```go
userInfo, err := fbLogin.GetUserAccount(channelID, token, "")
if err != nil {
    // 检查具体错误类型
    switch {
    case strings.Contains(err.Error(), "JWT verification failed"):
        log.Println("JWT token验证失败")
    case strings.Contains(err.Error(), "traditional access token verification failed"):
        log.Println("传统access token验证失败")
    default:
        log.Printf("未知错误: %v", err)
    }
}
```

## 配置说明

### Credentials结构

```go
type Credentials struct {
    ClientSecret string `json:"client_secret"` // Facebook App Secret
}
```

- `ClientSecret`: Facebook应用的App Secret，用于JWT HS256验证和传统验证

## 注意事项

1. **生产环境安全**：本实现不使用debug_token API，适合生产环境
2. **App Secret保护**：确保App Secret的安全存储，不要硬编码在代码中
3. **Token掩码**：日志中的token会自动进行掩码处理，保护敏感信息
4. **错误处理**：建议对不同类型的验证错误进行适当的处理和重试

## 测试

```bash
cd pkg/repo/sdk/facebook
go test -v
```

## 依赖

- `github.com/golang-jwt/jwt`: JWT处理
- `github.com/sirupsen/logrus`: 日志记录
- `git.keepfancy.xyz/back-end/frameworks/lib/httpx`: HTTP请求
