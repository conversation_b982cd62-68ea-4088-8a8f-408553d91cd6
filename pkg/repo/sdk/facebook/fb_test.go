package facebook

import (
	"encoding/json"
	"log"
	"testing"
)

func TestGetUserFriends(t *testing.T) {
	accessToken := "EAAd6E15hmfABO0KLr5vLIPd6c5cs6N4CTqjaqdrLRKnxMcZAFFWCAmKtSjZCVdnbkJrYAbNIo9XIbKpQL9nI9aeAnClhwNRcHHUOQnyNtqdZCBRcodaFfgjXcR3tLVBHiCFhNGvkhhnOrNeWjXZCDvfC7aZAjGPjHDUJioxYpxvNuV9h6krQ3tFP7HaEE2tmG1jifCfeLyb9wxCcdazT8deeJplTiqjG6zn1q9hYgnEZBVom5UZCkMoDaRblcEVUqkZD"
	//now := time.Now()
	rsp, err := GetFriends(accessToken, 10, "")
	if err != nil {
		t.Errorf("GetFriends err:%s", err)
		return
	}
	js, _ := json.Marshal(rsp)
	log.Printf("friendList:%s", js)
}

// TestIsJWTToken 测试JWT token识别
func TestIsJWTToken(t *testing.T) {
	tests := []struct {
		name     string
		token    string
		expected bool
	}{
		{
			name:     "Valid JWT token",
			token:    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
			expected: true,
		},
		{
			name:     "Access token",
			token:    "EAAd6E15hmfABO0KLr5vLIPd6c5cs6N4CTqjaqdrLRKnxMcZAFFWCAmKtSjZCVdnbkJrYAbNIo9XIbKpQL9nI9aeAnClhwNRcHHUOQnyNtqdZCBRcodaFfgjXcR3tLVBHiCFhNGvkhhnOrNeWjXZCDvfC7aZAjGPjHDUJioxYpxvNuV9h6krQ3tFP7HaEE2tmG1jifCfeLyb9wxCcdazT8deeJplTiqjG6zn1q9hYgnEZBVom5UZCkMoDaRblcEVUqkZD",
			expected: false,
		},
		{
			name:     "Empty token",
			token:    "",
			expected: false,
		},
		{
			name:     "Invalid format",
			token:    "invalid.token",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isJWTToken(tt.token)
			if result != tt.expected {
				t.Errorf("isJWTToken(%s) = %v, expected %v", tt.token, result, tt.expected)
			}
		})
	}
}

// TestMaskToken 测试token掩码功能
func TestMaskToken(t *testing.T) {
	tests := []struct {
		name     string
		token    string
		expected string
	}{
		{
			name:     "Normal token",
			token:    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
			expected: "eyJhbG...ssw5c",
		},
		{
			name:     "Short token",
			token:    "short",
			expected: "****",
		},
		{
			name:     "Empty token",
			token:    "",
			expected: "<empty>",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := maskToken(tt.token)
			if result != tt.expected {
				t.Errorf("maskToken(%s) = %s, expected %s", tt.token, result, tt.expected)
			}
		})
	}
}

func TestGetFriendsFinder(t *testing.T) {
	accessToken := "GGQVlhQ0ZAlOXlxaFIycDBqM2ZAvdzY4QlkwR2dGMXlTVWs5anp6dnNDS2dZAdTVaQkx4cEcxeVZAjbU5DeVVQdnVjX3dFVjJSV3hCRmlWMmN3V0dLS0VockRGa25nVmtwRkNRTmU1UExrel9FUFpIeDNyUll2STBEa1l2VlRWbDhfcGRHV1BPQlI5Vk50UjNnbnhMRjJTS05KMG56a3NwM3VScwZDZD"
	//now := time.Now()
	rsp, err := GetFinder(accessToken)
	if err != nil {
		t.Errorf("GetFriends err:%s", err)
		return
	}
	js, _ := json.Marshal(rsp)
	log.Printf("friendList:%s", js)
}
