package sdk

import (
	"fmt"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func TestGetUserInfo(t *testing.T) {
	tests := []struct {
		name        string
		accType     commonPB.LOGIN_TYPE
		accessToken string
		code        string
		wantErr     bool
	}{
		{
			name:        "测试Facebook登录",
			accType:     commonPB.LOGIN_TYPE_LT_FACEBOOK,
			accessToken: "EAATTGl4jCVoBOyB8S2H2MqPRHgYVQgMx9ek8DYZCjX3PMqQ9jaGm4QoH3vZB7PnRpCdtxRceZALhZBvFDZAPDhiq8Lb0yW0GWaZAtpS2RsDBlTQ2TXeVYFuxSHSBk2z160JgXPcynY6JWvunauxAGJpnNkiualhbdxeZCDIXxc6sDPh1U74sZAT827MlOf3mvteKCTICxtlyEPsxBZAvm52nVn5twilvF2AN4ZAJOcZCfmHDBDJoQoZAhWiVZCqjJteoAicpBZCIIeLFQZD", // 替换为有效的测试token
			code:        "",
			wantErr:     false,
		},
		{
			name:        "测试Google登录",
			accType:     commonPB.LOGIN_TYPE_LT_GOOGLE,
			accessToken: "your_google_test_token", // 替换为有效的测试token
			code:        "",
			wantErr:     false,
		},
		{
			name:        "测试Apple登录",
			accType:     commonPB.LOGIN_TYPE_LT_APPLE,
			accessToken: "",
			code:        "c484284452083460b9c509bc44c752bcb.0.szsu.aViGd_LOdvpk33i28TaJpg",
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			userInfo, err := GetUserInfo(&LoginParams{
				ChannelID:   commonPB.CHANNEL_TYPE_CT_MASTER,
				AccType:     tt.accType,
				AccessToken: tt.accessToken,
				Code:        tt.code,
			})
			t.Errorf("GetUserInfo() error = %v, wantErr %v", err, tt.wantErr)
			// 打印用户信息
			fmt.Printf("登录类型: %v\n", tt.accType)
			fmt.Printf("OpenID: %s\n", userInfo.OpenID)
			fmt.Printf("名称: %s\n", userInfo.Name)
			fmt.Printf("头像: %s\n", userInfo.Avatar)
			fmt.Printf("邮箱: %s\n", userInfo.Email)
			fmt.Printf("国家: %s\n", userInfo.Country)
		})
	}
}
