package testx

import (
	"context"
	"fmt"
	"path"
	"runtime"
	"strings"
	"sync"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/nsqx"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

func Init(server ...string) {
	var serverHost string
	if len(server) == 0 {
		serverHost = "************"
	} else {
		serverHost = server[0]
	}
	InitLog()
	InitRedis(serverHost)
	InitSql(serverHost)
	InitConsul(serverHost)
	InitNsq(serverHost)
}

// TestCtx 获取测试用context
func TestCtx(playerId uint64, channelType int32) context.Context {
	ctx := interceptor.NewRpcClientCtx(
		interceptor.WithPlayerId(playerId),
		interceptor.WithProductId(1),
		interceptor.WithChannelType(channelType),
	)
	return ctx
}

func InitRedis(server string) {
	addr := server + ":6379"
	passwd := "8888"
	conf := map[string]string{
		"addr":   server + ":6379",
		"passwd": "8888",
	}
	viper.Set(dict.ConfigRedisAddr, addr)
	viper.Set(dict.ConfigRedisPwd, passwd)
	viper.Set(dict.ConfigRedisList, map[string]interface{}{
		dict_redis.RDBPlayer:   conf, // 玩家库
		dict_redis.RDBGateway:  conf, // 网关库
		dict_redis.RDBRoom:     conf, // 房间库
		dict_redis.RDBGame:     conf, // 游戏库
		dict_redis.RDBLock:     conf, // 分布式锁库
		dict_redis.RDBAsset:    conf, // 资产库
		dict_redis.RDBTask:     conf, // 任务
		dict_redis.RDBGeneral:  conf, // 通用业务
		dict_redis.RDBBiz:      conf, // 其它业务
		dict_redis.RDBActivity: conf, // 活动
		dict_redis.RDBStream:   conf, // 实时数据库(如同步服中的玩家位置信息)
	})
}

func InitConsul(server string) {
	viper.SetDefault(dict.ConfigConsulAddr, server+":8500")
}

func InitLog() {
	logrus.SetLevel(logrus.DebugLevel)
	logrus.SetReportCaller(true)
	logrus.SetFormatter(&logrus.JSONFormatter{
		DisableTimestamp: false,
		// TimestampFormat:  "2006-01-02 15:04:05",
		TimestampFormat: time.RFC3339Nano,
		CallerPrettyfier: func(f *runtime.Frame) (string, string) {
			s := strings.Split(f.Function, ".")
			funcName := s[len(s)-1]
			filepath, filename := path.Split(f.File)
			return funcName, filepath + filename + fmt.Sprintf(":%d", f.Line)
		},
		PrettyPrint: false,
	})
}

func InitSql(server string) {
	dbList := []string{
		dict_mysql.MysqlDBPlayer,
		dict_mysql.MysqlDBAsset,
		dict_mysql.MysqlDBPay,
		dict_mysql.MysqlDBBiz,
		dict_mysql.MysqlDBTask,
		dict_mysql.MysqlDBGeneral,
	}
	mpSql := make(map[string]interface{})
	for _, db := range dbList {
		mpSql[db] = map[string]interface{}{
			"addr":   server + ":3306",
			"passwd": "fancydb2024#",
			"user":   "root",
			"db":     db,
		}
	}

	viper.SetDefault(dict.ConfigMysqlList, mpSql)
}

func InitNsq(server string) {
	viper.SetDefault(dict.ConfigNsqDAddr, server+":4150")
	viper.SetDefault(dict.ConfigNsqHttpAddr, server+":4151")
	viper.SetDefault(dict.ConfigNsqLookUpdAddress, server+":4161")
	nsqx.Setup()
}

// 并发测试接口
// ImmediateMultiGo(1000, func() {})
func ImmediateMultiGo(num int, f func()) {
	// 准备进程管理
	ctx, cancel := context.WithCancel(context.TODO())
	// 等待管理
	wg := sync.WaitGroup{}
	ready := sync.WaitGroup{}
	ready.Add(num)

	for i := 0; i < num; i++ {
		wg.Add(1)
		// n := i
		go func() {
			defer wg.Done()
			// 进程准备就绪
			ready.Done()
			select {
			case <-ctx.Done():
				f()
			}
		}()
	}
	ready.Wait()
	cancel()
	wg.Wait()
}

func InitKafka(server string) {
	viper.SetDefault("kafka-producer.brokers", []any{fmt.Sprintf("[%s:9092]", server)})
}
