syntax = "proto3";

package loginRpc;
option go_package = ".;loginRpc";

import "common.proto";
import "enum.proto";
import "errors.proto";


// 登录请求
message LoginReq {
    common.LOGIN_TYPE     login_type        = 1;  // 登录方式
    string                client_version    = 2;  // 客户端版本号
    int32                 product_id        = 3;  // 产品 ID
    common.CHANNEL_TYPE   channel_id        = 4;  // 渠道 ID
    common.DeviceInfo     device_info       = 5;  // 设备信息
    string                third_token       = 6;  // 三方Token，根据具体SDK
    // string                adjust_id         = 7;  // adjust id (废弃 device info 重复)
    common.NETWORK_TYPE   network           = 8;  // 网络类型
    string                bundle_name       = 9;  // 包名
    // string                ip                = 10; // IP
    common.PLATFORM_TYPE  platform          = 11; // 平台
    common.AccountInfo    account_info      = 12; // 账号信息(针对账号密码登录)
    bool                  is_reg            = 13; // 是否是注册
    common.ThirdLoginInfo third_info        = 14;  // 三方登录信息(针对三方登录)
}

message LoginRsp {
    common.Result        ret                  = 1;
    uint64               player_id            = 2;   // 玩家 ID
    string               token                = 3;   // 服务端返回token
    bool                 is_reg               = 4;   // 是否注册
    common.RichUserInfo  rich_user_info       = 5;   // 全量用户信息
    bool                 force_real_name      = 6;   // 是否需要强制实名
    bool                 is_in_anti_addiction = 7;   // 是否在防沉迷内          
    string               ori_identifier       = 8;   // 唯一标识符，标识设备
    bool                 white_list           = 9;   // 白名单
    bool                 real_name_auth       = 10;  // 实名状态
    common.GRAY_STRATEGY gray_strategy        = 11;  // 灰度策略
    int32                dyed_label           = 12;  // 染色标记 
}

// 登出请求
message LogoutReq {
    uint64 player_id = 1;
}

// 登出回应
message LogoutRsp {
    common.Result  ret = 1;
}

// LoginService 登录服务
service LoginService {
    rpc Login(LoginReq) returns(LoginRsp) {}
    rpc Logout(LogoutReq) returns(LogoutRsp) {}
}


