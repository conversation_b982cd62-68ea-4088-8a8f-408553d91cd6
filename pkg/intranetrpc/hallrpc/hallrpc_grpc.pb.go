// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: hallrpc/hallrpc.proto

package hallRpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HallService_OptPlayerItem_FullMethodName         = "/hallRpc.HallService/OptPlayerItem"
	HallService_QueryPlayerBaseInfo_FullMethodName   = "/hallRpc.HallService/QueryPlayerBaseInfo"
	HallService_GmLoadRule_FullMethodName            = "/hallRpc.HallService/GmLoadRule"
	HallService_QueryPlayerRodInfo_FullMethodName    = "/hallRpc.HallService/QueryPlayerRodInfo"
	HallService_QueryHolidayType_FullMethodName      = "/hallRpc.HallService/QueryHolidayType"
	HallService_LossRodDurability_FullMethodName     = "/hallRpc.HallService/LossRodDurability"
	HallService_LossItemHeap_FullMethodName          = "/hallRpc.HallService/LossItemHeap"
	HallService_SetPlayerRedDot_FullMethodName       = "/hallRpc.HallService/SetPlayerRedDot"
	HallService_QueryPlayerAllRodInfo_FullMethodName = "/hallRpc.HallService/QueryPlayerAllRodInfo"
)

// HallServiceClient is the client API for HallService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HallServiceClient interface {
	// 操作道具请求
	OptPlayerItem(ctx context.Context, in *OptPlayerItemReq, opts ...grpc.CallOption) (*OptPlayerItemRsp, error)
	// 查询玩家基础信息
	QueryPlayerBaseInfo(ctx context.Context, in *QueryPlayerBaseInfoReq, opts ...grpc.CallOption) (*QueryPlayerBaseInfoRsp, error)
	// 加载竿架规则
	GmLoadRule(ctx context.Context, in *GmLoadRuleReq, opts ...grpc.CallOption) (*GmLoadRuleRsp, error)
	// 查询玩家钓组信息
	QueryPlayerRodInfo(ctx context.Context, in *QueryPlayerRodInfoReq, opts ...grpc.CallOption) (*QueryPlayerRodInfoRsp, error)
	// 查询节假日类型
	QueryHolidayType(ctx context.Context, in *QueryHolidayTypeReq, opts ...grpc.CallOption) (*QueryHolidayTypeRsp, error)
	// 磨损钓竿耐久
	LossRodDurability(ctx context.Context, in *LossRodDurabilityReq, opts ...grpc.CallOption) (*LossRodDurabilityRsp, error)
	// 扣除鱼饵耐久
	LossItemHeap(ctx context.Context, in *LossItemHeapReq, opts ...grpc.CallOption) (*LossItemHeapRsp, error)
	// 设置玩家红点状态
	SetPlayerRedDot(ctx context.Context, in *SetPlayerRedDotReq, opts ...grpc.CallOption) (*SetPlayerRedDotRes, error)
	// 获取玩家所有钓组信息
	QueryPlayerAllRodInfo(ctx context.Context, in *QueryPlayerAllRodInfoReq, opts ...grpc.CallOption) (*QueryPlayerAllRodInfoRsp, error)
}

type hallServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHallServiceClient(cc grpc.ClientConnInterface) HallServiceClient {
	return &hallServiceClient{cc}
}

func (c *hallServiceClient) OptPlayerItem(ctx context.Context, in *OptPlayerItemReq, opts ...grpc.CallOption) (*OptPlayerItemRsp, error) {
	out := new(OptPlayerItemRsp)
	err := c.cc.Invoke(ctx, HallService_OptPlayerItem_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallServiceClient) QueryPlayerBaseInfo(ctx context.Context, in *QueryPlayerBaseInfoReq, opts ...grpc.CallOption) (*QueryPlayerBaseInfoRsp, error) {
	out := new(QueryPlayerBaseInfoRsp)
	err := c.cc.Invoke(ctx, HallService_QueryPlayerBaseInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallServiceClient) GmLoadRule(ctx context.Context, in *GmLoadRuleReq, opts ...grpc.CallOption) (*GmLoadRuleRsp, error) {
	out := new(GmLoadRuleRsp)
	err := c.cc.Invoke(ctx, HallService_GmLoadRule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallServiceClient) QueryPlayerRodInfo(ctx context.Context, in *QueryPlayerRodInfoReq, opts ...grpc.CallOption) (*QueryPlayerRodInfoRsp, error) {
	out := new(QueryPlayerRodInfoRsp)
	err := c.cc.Invoke(ctx, HallService_QueryPlayerRodInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallServiceClient) QueryHolidayType(ctx context.Context, in *QueryHolidayTypeReq, opts ...grpc.CallOption) (*QueryHolidayTypeRsp, error) {
	out := new(QueryHolidayTypeRsp)
	err := c.cc.Invoke(ctx, HallService_QueryHolidayType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallServiceClient) LossRodDurability(ctx context.Context, in *LossRodDurabilityReq, opts ...grpc.CallOption) (*LossRodDurabilityRsp, error) {
	out := new(LossRodDurabilityRsp)
	err := c.cc.Invoke(ctx, HallService_LossRodDurability_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallServiceClient) LossItemHeap(ctx context.Context, in *LossItemHeapReq, opts ...grpc.CallOption) (*LossItemHeapRsp, error) {
	out := new(LossItemHeapRsp)
	err := c.cc.Invoke(ctx, HallService_LossItemHeap_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallServiceClient) SetPlayerRedDot(ctx context.Context, in *SetPlayerRedDotReq, opts ...grpc.CallOption) (*SetPlayerRedDotRes, error) {
	out := new(SetPlayerRedDotRes)
	err := c.cc.Invoke(ctx, HallService_SetPlayerRedDot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hallServiceClient) QueryPlayerAllRodInfo(ctx context.Context, in *QueryPlayerAllRodInfoReq, opts ...grpc.CallOption) (*QueryPlayerAllRodInfoRsp, error) {
	out := new(QueryPlayerAllRodInfoRsp)
	err := c.cc.Invoke(ctx, HallService_QueryPlayerAllRodInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HallServiceServer is the server API for HallService service.
// All implementations should embed UnimplementedHallServiceServer
// for forward compatibility
type HallServiceServer interface {
	// 操作道具请求
	OptPlayerItem(context.Context, *OptPlayerItemReq) (*OptPlayerItemRsp, error)
	// 查询玩家基础信息
	QueryPlayerBaseInfo(context.Context, *QueryPlayerBaseInfoReq) (*QueryPlayerBaseInfoRsp, error)
	// 加载竿架规则
	GmLoadRule(context.Context, *GmLoadRuleReq) (*GmLoadRuleRsp, error)
	// 查询玩家钓组信息
	QueryPlayerRodInfo(context.Context, *QueryPlayerRodInfoReq) (*QueryPlayerRodInfoRsp, error)
	// 查询节假日类型
	QueryHolidayType(context.Context, *QueryHolidayTypeReq) (*QueryHolidayTypeRsp, error)
	// 磨损钓竿耐久
	LossRodDurability(context.Context, *LossRodDurabilityReq) (*LossRodDurabilityRsp, error)
	// 扣除鱼饵耐久
	LossItemHeap(context.Context, *LossItemHeapReq) (*LossItemHeapRsp, error)
	// 设置玩家红点状态
	SetPlayerRedDot(context.Context, *SetPlayerRedDotReq) (*SetPlayerRedDotRes, error)
	// 获取玩家所有钓组信息
	QueryPlayerAllRodInfo(context.Context, *QueryPlayerAllRodInfoReq) (*QueryPlayerAllRodInfoRsp, error)
}

// UnimplementedHallServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHallServiceServer struct {
}

func (UnimplementedHallServiceServer) OptPlayerItem(context.Context, *OptPlayerItemReq) (*OptPlayerItemRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OptPlayerItem not implemented")
}
func (UnimplementedHallServiceServer) QueryPlayerBaseInfo(context.Context, *QueryPlayerBaseInfoReq) (*QueryPlayerBaseInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPlayerBaseInfo not implemented")
}
func (UnimplementedHallServiceServer) GmLoadRule(context.Context, *GmLoadRuleReq) (*GmLoadRuleRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GmLoadRule not implemented")
}
func (UnimplementedHallServiceServer) QueryPlayerRodInfo(context.Context, *QueryPlayerRodInfoReq) (*QueryPlayerRodInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPlayerRodInfo not implemented")
}
func (UnimplementedHallServiceServer) QueryHolidayType(context.Context, *QueryHolidayTypeReq) (*QueryHolidayTypeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryHolidayType not implemented")
}
func (UnimplementedHallServiceServer) LossRodDurability(context.Context, *LossRodDurabilityReq) (*LossRodDurabilityRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LossRodDurability not implemented")
}
func (UnimplementedHallServiceServer) LossItemHeap(context.Context, *LossItemHeapReq) (*LossItemHeapRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LossItemHeap not implemented")
}
func (UnimplementedHallServiceServer) SetPlayerRedDot(context.Context, *SetPlayerRedDotReq) (*SetPlayerRedDotRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPlayerRedDot not implemented")
}
func (UnimplementedHallServiceServer) QueryPlayerAllRodInfo(context.Context, *QueryPlayerAllRodInfoReq) (*QueryPlayerAllRodInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPlayerAllRodInfo not implemented")
}

// UnsafeHallServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HallServiceServer will
// result in compilation errors.
type UnsafeHallServiceServer interface {
	mustEmbedUnimplementedHallServiceServer()
}

func RegisterHallServiceServer(s grpc.ServiceRegistrar, srv HallServiceServer) {
	s.RegisterService(&HallService_ServiceDesc, srv)
}

func _HallService_OptPlayerItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OptPlayerItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServiceServer).OptPlayerItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HallService_OptPlayerItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServiceServer).OptPlayerItem(ctx, req.(*OptPlayerItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HallService_QueryPlayerBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPlayerBaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServiceServer).QueryPlayerBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HallService_QueryPlayerBaseInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServiceServer).QueryPlayerBaseInfo(ctx, req.(*QueryPlayerBaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HallService_GmLoadRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GmLoadRuleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServiceServer).GmLoadRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HallService_GmLoadRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServiceServer).GmLoadRule(ctx, req.(*GmLoadRuleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HallService_QueryPlayerRodInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPlayerRodInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServiceServer).QueryPlayerRodInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HallService_QueryPlayerRodInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServiceServer).QueryPlayerRodInfo(ctx, req.(*QueryPlayerRodInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HallService_QueryHolidayType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryHolidayTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServiceServer).QueryHolidayType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HallService_QueryHolidayType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServiceServer).QueryHolidayType(ctx, req.(*QueryHolidayTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HallService_LossRodDurability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LossRodDurabilityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServiceServer).LossRodDurability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HallService_LossRodDurability_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServiceServer).LossRodDurability(ctx, req.(*LossRodDurabilityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HallService_LossItemHeap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LossItemHeapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServiceServer).LossItemHeap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HallService_LossItemHeap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServiceServer).LossItemHeap(ctx, req.(*LossItemHeapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HallService_SetPlayerRedDot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPlayerRedDotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServiceServer).SetPlayerRedDot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HallService_SetPlayerRedDot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServiceServer).SetPlayerRedDot(ctx, req.(*SetPlayerRedDotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HallService_QueryPlayerAllRodInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPlayerAllRodInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HallServiceServer).QueryPlayerAllRodInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HallService_QueryPlayerAllRodInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HallServiceServer).QueryPlayerAllRodInfo(ctx, req.(*QueryPlayerAllRodInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

// HallService_ServiceDesc is the grpc.ServiceDesc for HallService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HallService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hallRpc.HallService",
	HandlerType: (*HallServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OptPlayerItem",
			Handler:    _HallService_OptPlayerItem_Handler,
		},
		{
			MethodName: "QueryPlayerBaseInfo",
			Handler:    _HallService_QueryPlayerBaseInfo_Handler,
		},
		{
			MethodName: "GmLoadRule",
			Handler:    _HallService_GmLoadRule_Handler,
		},
		{
			MethodName: "QueryPlayerRodInfo",
			Handler:    _HallService_QueryPlayerRodInfo_Handler,
		},
		{
			MethodName: "QueryHolidayType",
			Handler:    _HallService_QueryHolidayType_Handler,
		},
		{
			MethodName: "LossRodDurability",
			Handler:    _HallService_LossRodDurability_Handler,
		},
		{
			MethodName: "LossItemHeap",
			Handler:    _HallService_LossItemHeap_Handler,
		},
		{
			MethodName: "SetPlayerRedDot",
			Handler:    _HallService_SetPlayerRedDot_Handler,
		},
		{
			MethodName: "QueryPlayerAllRodInfo",
			Handler:    _HallService_QueryPlayerAllRodInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "hallrpc/hallrpc.proto",
}
