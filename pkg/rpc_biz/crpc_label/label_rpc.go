package crpc_label

import (
	"context"
	"fmt"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	labelRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/labelrpc"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type LabelRpcClient struct {
}

var (
	once     = &sync.Once{}
	instance *LabelRpcClient
)

func GetLabelRpcInstance() *LabelRpcClient {
	once.Do(func() {
		if instance == nil {
			instance = &LabelRpcClient{}
		}
	})
	return instance
}

// GetMsgRpcClient 获取MsgRpc服务的client
func (l *LabelRpcClient) GetMsgRpcClient() labelRpc.LabelServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.LabelSrv)

	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return labelRpc.NewLabelServiceClient(cc)
}

// RpcPushLabelEvent 推送标签事件
func RpcPushLabelEvent(ctx context.Context, req *labelRpc.LabelEventInfo) (*commonPB.Result, error) {
	rpcLabelCli := GetLabelRpcInstance().GetMsgRpcClient()
	if rpcLabelCli == nil {
		return nil, fmt.Errorf("rpc label client is nil")
	}

	ret, err := rpcLabelCli.PushLabelEvent(ctx, req)
	if err != nil || ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("push player:%d label event failed, req:%s, err:%v", req.GetPlayerId(), req.String(), err)
		return nil, err
	}

	return ret, nil
}

// RpcQueryPlayerLabel 查询玩家标签
func RpcQueryPlayerLabel(ctx context.Context, req *labelRpc.GetPlayerLabelListReq) ([]int64, error) {
	rpcLabelCli := GetLabelRpcInstance().GetMsgRpcClient()
	if rpcLabelCli == nil {
		return nil, fmt.Errorf("rpc label client is nil")
	}

	rsp, err := rpcLabelCli.QueryPlayerLabelList(ctx, req)
	if err != nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("query player:%d label info failed, req:%s, err:%v", req.GetPlayerId(), req.String(), err)
		return nil, err
	}

	return rsp.GetLabelList(), nil
}
