package crpc_rank

import (
	"sync"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	rankRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/rankrpc"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type RankRpcClient struct {
}

var (
	once     = &sync.Once{}
	instance *RankRpcClient
)

func GetRankRpcInstance() *RankRpcClient {
	once.Do(func() {
		if instance == nil {
			instance = &RankRpcClient{}
		}
	})
	return instance
}

// GetRankRpcClient 获取RankRpc服务的client
func (h *RankRpcClient) GetRankRpcClient() rankRpc.RankServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.RankSrv)

	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return rankRpc.NewRankServiceClient(cc)
}
