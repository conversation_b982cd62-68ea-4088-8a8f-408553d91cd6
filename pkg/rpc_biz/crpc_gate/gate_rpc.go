package crpc_gate

import (
	"context"
	"errors"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/gatewayrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	intranet_grpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

type GatewayRpcClient struct {
}

var (
	once   = &sync.Once{}
	single *GatewayRpcClient
)

func GetGateClient() *GatewayRpcClient {
	once.Do(func() {
		if single == nil {
			single = &GatewayRpcClient{}
		}
	})
	return single
}

// 获取客GateRPC服务的client
func (c *GatewayRpcClient) getGatewayRpcClient() gatewayrpc.GateServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.GateSrv)
	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return gatewayrpc.NewGateServiceClient(cc)
}

// 获取网关sender rpc client
func (c *GatewayRpcClient) getMsgSenderClient() intranet_grpc.MessageSenderClient {
	cc, err := grpcHa.GetConnection(dict_common.GateSrv)
	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return intranet_grpc.NewMessageSenderClient(cc)
}

// getMsgSenderClientByAddr 根据地址获取指定网关的rpc连接
func (c *GatewayRpcClient) getMsgSenderClientByAddr(addr string) intranet_grpc.MessageSenderClient {
	cc, err := grpcHa.GetConnectionByAddr(addr)
	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return intranet_grpc.NewMessageSenderClient(cc)
}

// SendPackageByPlayerID 通过GateWay向指定玩家ID的client发送通知消息
func (c *GatewayRpcClient) SendPackageByPlayerID(ctx context.Context, playerID uint64, msgID commonPB.MsgID, msgBody proto.Message) error {
	return c.BroadcastPackageByPlayerID(ctx, []uint64{playerID}, msgID, msgBody)
}

// BroadcastPackageByPlayerID 通过GateWay向多个指定玩家ID的client发送通知消息
func (c *GatewayRpcClient) BroadcastPackageByPlayerID(ctx context.Context, playerIDs []uint64, cmd commonPB.MsgID, body proto.Message) error {
	data, err := proto.Marshal(body)
	if err != nil {
		logrus.Errorf("消息 [%d] 序列化失败: %v", cmd, err)
		return errors.New("消息序列化失败")
	}

	_, err = c.getMsgSenderClient().SendMessage(ctx,
		&intranet_grpc.SendMessageRequest{
			PlayerId: playerIDs,
			Header: &intranet_grpc.Header{
				MsgId: uint32(cmd),
			},
			Data: data,
		})

	if err != nil {
		logrus.Errorf("grpc 调用失败：%v", err)
		return errors.New("消息发送异常")
	}

	return nil
}

// BroadcastPackageByPlayerIdAddr 根据玩家列表和网关地址发送到指定网关client通知消息
func (c *GatewayRpcClient) BroadcastPackageByPlayerIdAddr(ctx context.Context, playerAddr map[uint64]string, cmd commonPB.MsgID, body proto.Message) error {
	data, err := proto.Marshal(body)
	if err != nil {
		logrus.Errorf("消息 [%d] 序列化失败: %v", cmd, err)
		return errors.New("消息序列化失败")
	}

	// 将玩家按照所在网关服分组
	groups := make(map[string][]uint64)
	for playerId, addr := range playerAddr {
		groups[addr] = append(groups[addr], playerId)
	}

	for addr, playerArr := range groups {
		gateCli := c.getMsgSenderClientByAddr(addr)
		if gateCli == nil {
			gateCli = c.getMsgSenderClient()
			logrus.Errorf("网关地址:%s rpc地址为空", addr)
		}

		if gateCli == nil {
			continue
		}

		_, err = gateCli.SendMessage(ctx,
			&intranet_grpc.SendMessageRequest{
				PlayerId: playerArr,
				Header: &intranet_grpc.Header{
					MsgId: uint32(cmd),
				},
				Data: data,
			})

		if err != nil {
			logrus.Errorf("grpc 调用失败：%v", err)
		}
	}

	return err
}

// GetPlayerIPs 批量获取玩家 ip 地址
func (c *GatewayRpcClient) GetPlayerIPs(playerIDs []uint64) map[uint64]string {
	rsp, err := c.getGatewayRpcClient().GetPlayerIP(context.Background(),
		&gatewayrpc.GetPlayerIPRequest{
			PlayerIds: playerIDs,
		})
	if err != nil {
		logrus.Errorf("grpc 调用失败：%v", err)
		return map[uint64]string{}
	}

	address := rsp.GetAddrs()
	if address == nil {
		return map[uint64]string{}
	}

	return address
}

// GetPlayerGateAddr 获取玩家所在网关服地址
func (c *GatewayRpcClient) GetPlayerGateAddr(playerID uint64) string {
	return c.GetPlayerGateAddress([]uint64{playerID})[playerID]
}

// GetPlayerGateAddress 获取多个玩家所在网关地址
func (c *GatewayRpcClient) GetPlayerGateAddress(playerIDs []uint64) map[uint64]string {
	rsp, err := c.getGatewayRpcClient().GetPlayerGatewayAddr(context.Background(),
		&gatewayrpc.GetPlayerGatewayAddrRequest{
			PlayerIds: playerIDs,
		})
	if err != nil {
		logrus.Errorf("grpc 调用失败：%v", err)
		return map[uint64]string{}
	}

	address := rsp.GetAddrs()
	if address == nil {
		return map[uint64]string{}
	}

	return address
}

// GetOnlinePlayerIDs 获取所有在线玩家ID
func (c *GatewayRpcClient) GetOnlinePlayerIDs() *[]uint64 {
	rCli := redisx.GetGatewayCli()

	// TODO 需要网关实现在线人数

	k := "player:online"
	var ctx = context.Background()
	sMembers := rCli.SMembers(ctx, k)
	if sMembers.Err() != nil {
		logrus.Errorln("获取所有在线玩家ID失败")
	}

	var playerIDs []uint64
	if err := sMembers.ScanSlice(&playerIDs); err != nil {
		logrus.WithError(err).Errorln("获取所有在线玩家ID失败")
	}

	return &playerIDs
}

// BroadcastGlobal 全服广播
// 这里实现是通过RPC到一个网关，然后Pick到的网关会处理通知所有网关推送
func (c *GatewayRpcClient) BroadcastGlobal(ctx context.Context, cmd commonPB.MsgID, body proto.Message) error {
	data, err := proto.Marshal(body)
	if err != nil {
		logrus.Errorf("BroadcastGlobal 消息 [%d] 序列化失败: %v", cmd, err)
		return errors.New("BroadcastGlobal 消息序列化失败")
	}

	_, err = c.getMsgSenderClient().BroadcastMessage(ctx,
		&intranet_grpc.BroadcastMsgRequest{
			Header: &intranet_grpc.Header{
				MsgId: uint32(cmd),
			},
			Data: data,
		})
	if err != nil {
		logrus.Warnf("BroadcastGlobal err：%v msgid:%d", err, cmd)
		return errors.New("BroadcastGlobal 消息发送异常")
	}

	return nil
}
