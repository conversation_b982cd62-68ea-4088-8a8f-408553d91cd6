package crpc_trip

import (
	"context"
	"fmt"
	"sync"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	tripRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/triprpc"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type TripRpcClient struct {
}

var (
	once     = &sync.Once{}
	instance *TripRpcClient
)

func GetTripRpcInstance() *TripRpcClient {
	once.Do(func() {
		if instance == nil {
			instance = &TripRpcClient{}
		}
	})

	return instance
}

// GetTripRpcClient 获取TripRpc服务的client
func (t *TripRpcClient) GetTripRpcClient() tripRpc.TripServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.TripSrv)

	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return tripRpc.NewTripServiceClient(cc)
}

// RpcGetPlayerRoomInfo RPC到TripSrv 获取玩家房间信息
func RpcGetPlayerRoomInfo(ctx context.Context, playerID uint64) (*tripRpc.QueryTripRsp, error) {
	tripRpcClient := GetTripRpcInstance().GetTripRpcClient()

	if tripRpcClient == nil {
		return nil, fmt.Errorf("trip rpc client is nil")
	}

	reqRpc := &tripRpc.QueryTripReq{PlayerId: playerID}

	hRet, errRet := tripRpcClient.QueryTrip(ctx, reqRpc)
	return hRet, errRet
}

// RpcGetRoomPlayerInfo RPC到TripSrv 获取房间内玩家信息
func RpcGetRoomPlayerInfo(ctx context.Context, roomId string) (*tripRpc.QueryRoomPlayersRsp, error) {
	tripRpcClient := GetTripRpcInstance().GetTripRpcClient()

	if tripRpcClient == nil {
		return nil, fmt.Errorf("trip rpc client is nil")
	}

	reqRpc := &tripRpc.QueryRoomPlayersReq{RoomId: roomId}
	hRet, errRet := tripRpcClient.QueryRoomPlayers(ctx, reqRpc)

	return hRet, errRet
}
