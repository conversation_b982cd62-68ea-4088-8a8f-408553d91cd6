package crpc_hall

import (
	"context"
	"fmt"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	hallRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hallrpc"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type HallRpcClient struct {
}

var (
	once     = &sync.Once{}
	instance *HallRpcClient
)

func GetHallRpcInstance() *HallRpcClient {
	once.Do(func() {
		if instance == nil {
			instance = &HallRpcClient{}
		}
	})
	return instance
}

// GetHallRpcClient 获取HallRpc服务的client
func (h *HallRpcClient) GetHallRpcClient() hallRpc.HallServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.HallSrv)

	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return hallRpc.NewHallServiceClient(cc)
}

// 通用接口
// RpcOptPlayerItem 操作玩家道具
func RpcOptPlayerItem(ctx context.Context, req *hallRpc.OptPlayerItemReq) (*commonPB.Reward, error) {
	rpcHallCli := GetHallRpcInstance().GetHallRpcClient()
	if rpcHallCli == nil {
		return nil, fmt.Errorf("rpc hall client is nil")
	}

	rsp, err := rpcHallCli.OptPlayerItem(ctx, req)
	if err != nil {
		logrus.Errorf("add player:%d item failed, req:%s, err:%v", req.GetPlayerId(), req.String(), err)
		return nil, err
	}
	if rsp == nil {
		return nil, fmt.Errorf("add player:%d item failed, rsp is nil", req.GetPlayerId())
	}
	if rsp.GetRet() != nil && rsp.GetRet().Code != commonPB.ErrCode_ERR_SUCCESS {
		return nil, fmt.Errorf("add player:%d item failed, ret:%s", req.GetPlayerId(), rsp.GetRet().String())
	}

	return rsp.RewardInfo, nil
}

// 通用接口
// RpcQueryPlayerBaseInfo 查询玩家基础信息
func RpcQueryPlayerBaseInfo(ctx context.Context, playerId uint64) (*commonPB.PlayerBaseInfo, error) {
	if playerId <= 0 {
		return nil, fmt.Errorf("playerId is invalid")
	}

	rpcHallCli := GetHallRpcInstance().GetHallRpcClient()
	if rpcHallCli == nil {
		return nil, fmt.Errorf("rpc hall client is nil")
	}

	req := &hallRpc.QueryPlayerBaseInfoReq{
		PlayerId: playerId,
	}

	rsp, err := rpcHallCli.QueryPlayerBaseInfo(ctx, req)

	if err != nil {
		logrus.Errorf("add player:%d item failed, req:%s, err:%v", req.GetPlayerId(), req.String(), err)
		return nil, err
	}
	if rsp == nil || rsp.PlayerInfo == nil {
		return nil, fmt.Errorf("player info is nil")
	}

	return rsp.PlayerInfo, nil
}

// RpcQueryPlayerRodInfo 查询杆包
func RpcQueryPlayerRodInfo(ctx context.Context, playerId uint64, id int32) (*commonPB.RodBagInfo, error) {
	if playerId <= 0 {
		return nil, fmt.Errorf("playerId is invalid")
	}

	rpcHallCli := GetHallRpcInstance().GetHallRpcClient()
	if rpcHallCli == nil {
		return nil, fmt.Errorf("rpc hall client is nil")
	}

	req := &hallRpc.QueryPlayerRodInfoReq{
		PlayerId: playerId,
		Id:       id,
	}
	rsp, err := rpcHallCli.QueryPlayerRodInfo(ctx, req)
	if err != nil {
		return nil, err
	}

	return rsp.GetRodInfo(), err
}

// RpcQueryPlayerAllRodInfo 查询玩家所有杆包
func RpcQueryPlayerAllRodInfo(ctx context.Context, playerId uint64) ([]*commonPB.RodBagInfo, error) {
	if playerId <= 0 {
		return nil, fmt.Errorf("playerId is invalid")
	}

	rpcHallCli := GetHallRpcInstance().GetHallRpcClient()
	if rpcHallCli == nil {
		return nil, fmt.Errorf("rpc hall client is nil")
	}

	req := &hallRpc.QueryPlayerAllRodInfoReq{
		PlayerId: playerId,
	}

	rsp, err := rpcHallCli.QueryPlayerAllRodInfo(ctx, req)
	if err != nil {
		return nil, err
	}

	return rsp.GetRodInfoArr(), err
}
