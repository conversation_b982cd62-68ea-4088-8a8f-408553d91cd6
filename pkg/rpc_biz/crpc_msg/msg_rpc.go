package crpc_msg

import (
	"sync"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	msgRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/msgrpc"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type MsgRpcClient struct {
}

var (
	once     = &sync.Once{}
	instance *MsgRpcClient
)

func GetMsgRpcInstance() *MsgRpcClient {
	once.Do(func() {
		if instance == nil {
			instance = &MsgRpcClient{}
		}
	})
	return instance
}

// GetMsgRpcClient 获取MsgRpc服务的client
func (h *MsgRpcClient) GetMsgRpcClient() msgRpc.MsgServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.MsgSrv)

	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return msgRpc.NewMsgServiceClient(cc)
}
