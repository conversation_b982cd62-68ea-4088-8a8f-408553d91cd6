package crpc_sync

import (
	"sync"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	syncRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/syncrpc"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type SyncRpcClient struct {
}

var (
	once     = &sync.Once{}
	instance *SyncRpcClient
)

func GetWorldRpcInstance() *SyncRpcClient {
	once.Do(func() {
		if instance == nil {
			instance = &SyncRpcClient{}
		}
	})
	return instance
}

// GetSyncRpcClient 获取SyncRpc服务的client
func (u *SyncRpcClient) GetSyncRpcClient() syncRpc.SyncServiceClient {

	cc, err := grpcHa.GetConnection(dict_common.SyncSrv)
	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return syncRpc.NewSyncServiceClient(cc)
}
