// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: app_info.proto

package appInfoPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AppInfoReq App信息
type AppInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppVersion  string               `protobuf:"bytes,1,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`                      // 客户端版本
	AppLanguage string               `protobuf:"bytes,2,opt,name=app_language,json=appLanguage,proto3" json:"app_language,omitempty"`                   // 语言
	ProductId   common.PRODUCT_ID    `protobuf:"varint,3,opt,name=product_id,json=productId,proto3,enum=common.PRODUCT_ID" json:"product_id,omitempty"` // 产品ID
	Channel     common.CHANNEL_TYPE  `protobuf:"varint,4,opt,name=channel,proto3,enum=common.CHANNEL_TYPE" json:"channel,omitempty"`                    // 渠道
	Platform    common.PLATFORM_TYPE `protobuf:"varint,5,opt,name=platform,proto3,enum=common.PLATFORM_TYPE" json:"platform,omitempty"`                 // 平台类型
}

func (x *AppInfoReq) Reset() {
	*x = AppInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppInfoReq) ProtoMessage() {}

func (x *AppInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_app_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppInfoReq.ProtoReflect.Descriptor instead.
func (*AppInfoReq) Descriptor() ([]byte, []int) {
	return file_app_info_proto_rawDescGZIP(), []int{0}
}

func (x *AppInfoReq) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *AppInfoReq) GetAppLanguage() string {
	if x != nil {
		return x.AppLanguage
	}
	return ""
}

func (x *AppInfoReq) GetProductId() common.PRODUCT_ID {
	if x != nil {
		return x.ProductId
	}
	return common.PRODUCT_ID(0)
}

func (x *AppInfoReq) GetChannel() common.CHANNEL_TYPE {
	if x != nil {
		return x.Channel
	}
	return common.CHANNEL_TYPE(0)
}

func (x *AppInfoReq) GetPlatform() common.PLATFORM_TYPE {
	if x != nil {
		return x.Platform
	}
	return common.PLATFORM_TYPE(0)
}

type AppInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret          *common.Result          `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	InfoUpdate   *common.AppUpdateInfo   `protobuf:"bytes,2,opt,name=info_update,json=infoUpdate,proto3" json:"info_update,omitempty"`
	InfoResource *common.AppResourceInfo `protobuf:"bytes,3,opt,name=info_resource,json=infoResource,proto3" json:"info_resource,omitempty"`
	InfoAddress  *common.AppAddressInfo  `protobuf:"bytes,4,opt,name=info_address,json=infoAddress,proto3" json:"info_address,omitempty"`
}

func (x *AppInfoRsp) Reset() {
	*x = AppInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_info_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppInfoRsp) ProtoMessage() {}

func (x *AppInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_app_info_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppInfoRsp.ProtoReflect.Descriptor instead.
func (*AppInfoRsp) Descriptor() ([]byte, []int) {
	return file_app_info_proto_rawDescGZIP(), []int{1}
}

func (x *AppInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *AppInfoRsp) GetInfoUpdate() *common.AppUpdateInfo {
	if x != nil {
		return x.InfoUpdate
	}
	return nil
}

func (x *AppInfoRsp) GetInfoResource() *common.AppResourceInfo {
	if x != nil {
		return x.InfoResource
	}
	return nil
}

func (x *AppInfoRsp) GetInfoAddress() *common.AppAddressInfo {
	if x != nil {
		return x.InfoAddress
	}
	return nil
}

// ServerInfoReq 服务器信息请求
type ServerInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32 `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` // 产品ID
	Channel   int32 `protobuf:"varint,2,opt,name=channel,proto3" json:"channel,omitempty"`                      // 渠道
}

func (x *ServerInfoReq) Reset() {
	*x = ServerInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_info_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerInfoReq) ProtoMessage() {}

func (x *ServerInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_app_info_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerInfoReq.ProtoReflect.Descriptor instead.
func (*ServerInfoReq) Descriptor() ([]byte, []int) {
	return file_app_info_proto_rawDescGZIP(), []int{2}
}

func (x *ServerInfoReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *ServerInfoReq) GetChannel() int32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

// ServerInfoRes 服务器信息响应
type ServerInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result  `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                           // 返回结果
	TimeInfo *ServerTimeInfo `protobuf:"bytes,2,opt,name=time_info,json=timeInfo,proto3" json:"time_info,omitempty"` // 服务器时间信息
}

func (x *ServerInfoRes) Reset() {
	*x = ServerInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_info_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerInfoRes) ProtoMessage() {}

func (x *ServerInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_app_info_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerInfoRes.ProtoReflect.Descriptor instead.
func (*ServerInfoRes) Descriptor() ([]byte, []int) {
	return file_app_info_proto_rawDescGZIP(), []int{3}
}

func (x *ServerInfoRes) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ServerInfoRes) GetTimeInfo() *ServerTimeInfo {
	if x != nil {
		return x.TimeInfo
	}
	return nil
}

// ServerTimeRes 服务器时间响应
type ServerTimeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Datetime  string `protobuf:"bytes,1,opt,name=datetime,proto3" json:"datetime,omitempty"`    // 格式化的日期时间
	Timestamp int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // Unix 时间戳（秒）
	Timezone  string `protobuf:"bytes,3,opt,name=timezone,proto3" json:"timezone,omitempty"`    // 时区信息
	Offset    int64  `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`       // 时区偏移量(秒)
}

func (x *ServerTimeInfo) Reset() {
	*x = ServerTimeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_app_info_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerTimeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerTimeInfo) ProtoMessage() {}

func (x *ServerTimeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_app_info_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerTimeInfo.ProtoReflect.Descriptor instead.
func (*ServerTimeInfo) Descriptor() ([]byte, []int) {
	return file_app_info_proto_rawDescGZIP(), []int{4}
}

func (x *ServerTimeInfo) GetDatetime() string {
	if x != nil {
		return x.Datetime
	}
	return ""
}

func (x *ServerTimeInfo) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ServerTimeInfo) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *ServerTimeInfo) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

var File_app_info_proto protoreflect.FileDescriptor

var file_app_info_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xe6, 0x01, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x4c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x49, 0x44, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x31, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0xdf, 0x01, 0x0a, 0x0a,
	0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x36, 0x0a, 0x0b,
	0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x70, 0x70, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x69, 0x6e, 0x66, 0x6f, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x3c, 0x0a, 0x0d, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x69, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x39, 0x0a, 0x0c, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x41, 0x70, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0b, 0x69, 0x6e, 0x66, 0x6f, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x48, 0x0a,
	0x0d, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x68, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x35, 0x0a, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54,
	0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x7e, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a,
	0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x42, 0x0c, 0x5a, 0x0a, 0x2f, 0x61, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x42, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_app_info_proto_rawDescOnce sync.Once
	file_app_info_proto_rawDescData = file_app_info_proto_rawDesc
)

func file_app_info_proto_rawDescGZIP() []byte {
	file_app_info_proto_rawDescOnce.Do(func() {
		file_app_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_app_info_proto_rawDescData)
	})
	return file_app_info_proto_rawDescData
}

var file_app_info_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_app_info_proto_goTypes = []interface{}{
	(*AppInfoReq)(nil),             // 0: app_info.AppInfoReq
	(*AppInfoRsp)(nil),             // 1: app_info.AppInfoRsp
	(*ServerInfoReq)(nil),          // 2: app_info.ServerInfoReq
	(*ServerInfoRes)(nil),          // 3: app_info.ServerInfoRes
	(*ServerTimeInfo)(nil),         // 4: app_info.ServerTimeInfo
	(common.PRODUCT_ID)(0),         // 5: common.PRODUCT_ID
	(common.CHANNEL_TYPE)(0),       // 6: common.CHANNEL_TYPE
	(common.PLATFORM_TYPE)(0),      // 7: common.PLATFORM_TYPE
	(*common.Result)(nil),          // 8: common.Result
	(*common.AppUpdateInfo)(nil),   // 9: common.AppUpdateInfo
	(*common.AppResourceInfo)(nil), // 10: common.AppResourceInfo
	(*common.AppAddressInfo)(nil),  // 11: common.AppAddressInfo
}
var file_app_info_proto_depIdxs = []int32{
	5,  // 0: app_info.AppInfoReq.product_id:type_name -> common.PRODUCT_ID
	6,  // 1: app_info.AppInfoReq.channel:type_name -> common.CHANNEL_TYPE
	7,  // 2: app_info.AppInfoReq.platform:type_name -> common.PLATFORM_TYPE
	8,  // 3: app_info.AppInfoRsp.ret:type_name -> common.Result
	9,  // 4: app_info.AppInfoRsp.info_update:type_name -> common.AppUpdateInfo
	10, // 5: app_info.AppInfoRsp.info_resource:type_name -> common.AppResourceInfo
	11, // 6: app_info.AppInfoRsp.info_address:type_name -> common.AppAddressInfo
	8,  // 7: app_info.ServerInfoRes.ret:type_name -> common.Result
	4,  // 8: app_info.ServerInfoRes.time_info:type_name -> app_info.ServerTimeInfo
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_app_info_proto_init() }
func file_app_info_proto_init() {
	if File_app_info_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_app_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_info_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_info_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_info_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_app_info_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerTimeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_app_info_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_app_info_proto_goTypes,
		DependencyIndexes: file_app_info_proto_depIdxs,
		MessageInfos:      file_app_info_proto_msgTypes,
	}.Build()
	File_app_info_proto = out.File
	file_app_info_proto_rawDesc = nil
	file_app_info_proto_goTypes = nil
	file_app_info_proto_depIdxs = nil
}
